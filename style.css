:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #f39c12;
    --danger-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --text-color: #333;
    --border-color: #ddd;
    --sidebar-width: 250px;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --success-color: #2ecc71;

    /* Responsive breakpoints */
    --mobile-breakpoint: 768px;
    --tablet-breakpoint: 992px;
    --desktop-breakpoint: 1200px;
}

/* Accessibility improvements */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only-focusable:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: 0.25rem 0.5rem !important;
    margin: 0 !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
}

/* Focus indicators for accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
.btn:focus,
input:focus,
select:focus,
textarea:focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: var(--text-color);
}

/* Sidebar Styles */
.sidebar {
    background-color: var(--dark-color);
    color: white;
    min-height: 100vh;
    padding: 0;
    position: sticky;
    top: 0;
}

.sidebar-header {
    padding: 20px 15px;
    background-color: rgba(0, 0, 0, 0.2);
    text-align: center;
}

.user-info {
    padding: 10px 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 600;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.8;
}

.sidebar .nav-item {
    width: 100%;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-left: 3px solid transparent;
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Main Content Styles */
.main-content {
    padding: 20px;
}

.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--border-color);
    padding: 15px 20px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Form Styles */
.form-label {
    font-weight: 500;
}

.form-control, .form-select {
    border-radius: 4px;
    border: 1px solid var(--border-color);
    padding: 8px 12px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.btn {
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

/* Table Styles */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-color);
    vertical-align: middle;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td, .table th {
    padding: 12px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Dashboard Styles */
.dashboard-card {
    text-align: center;
    padding: 20px;
}

.dashboard-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
    color: var(--primary-color);
}

/* Report Preview Styles */
.report-preview {
    min-height: 400px;
    padding: 20px;
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sidebar {
        position: static;
        min-height: auto;
    }

    .sidebar-header {
        text-align: left;
    }

    .sidebar .nav {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .sidebar .nav-item {
        width: auto;
    }

    .sidebar .nav-link {
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .sidebar .nav-link:hover, .sidebar .nav-link.active {
        border-left: none;
        border-bottom: 3px solid var(--primary-color);
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 10px;
    }

    .card-header, .card-body {
        padding: 15px;
    }

    .btn {
        padding: 6px 12px;
    }
}

/* Custom Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Attendance Styles */
.attendance-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-present {
    background-color: var(--success-color);
}

.status-absent {
    background-color: var(--danger-color);
}

.status-late {
    background-color: var(--warning-color);
}

/* Calendar Styles */
.calendar-cell {
    height: 100px;
    width: 14.28%;
    vertical-align: top;
    padding: 5px;
    position: relative;
}

.empty-cell {
    background-color: #f8f9fa;
}

.today {
    background-color: #e8f4ff;
}

.date-number {
    font-weight: bold;
    margin-bottom: 5px;
}

.events-container {
    font-size: 0.8rem;
}

.event-item {
    padding: 2px 4px;
    margin-bottom: 2px;
    border-radius: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.event-general {
    background-color: #6c757d;
    color: white;
}

.event-exam {
    background-color: #dc3545;
    color: white;
}

.event-holiday {
    background-color: #28a745;
    color: white;
}

.event-meeting {
    background-color: #17a2b8;
    color: white;
}

.event-activity {
    background-color: #ffc107;
    color: black;
}

.calendar-legend {
    font-size: 0.8rem;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-color {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 5px;
    border-radius: 3px;
}

/* Curriculum Styles */
.curriculum-topic {
    font-weight: 600;
    margin-bottom: 5px;
}

.curriculum-description {
    font-size: 0.9rem;
    color: #666;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-accent {
    color: var(--accent-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-accent {
    background-color: var(--accent-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

/* Print Styles for Grade Sheets and Report Cards */
@media print {
    /* Page setup for different document types - No gaps */
    @page {
        margin: 0;
        padding: 0;
        size: A4;
    }

    /* Landscape pages for class grade sheets (2 students per page) */
    .report-card > div[style*="29.7cm"] {
        width: 100% !important;
        height: 100vh !important;
        max-height: 21cm !important;
        margin: 0 !important;
        padding: 0.5cm !important;
        page-break-after: always !important;
        page-break-inside: avoid !important;
        overflow: hidden !important;
        position: relative !important;
        box-sizing: border-box !important;
    }

    /* Reset and base styles */
    body {
        margin: 0 !important;
        padding: 0 !important;
        font-family: 'Times New Roman', serif !important;
        font-size: 12px !important;
        line-height: 1.3 !important;
        color: black !important;
        background: white !important;
    }

    /* Hide all interface elements */
    body * {
        visibility: hidden;
    }

    /* Show only report content - No spacing */
    .report-preview,
    .report-preview *,
    .report-card,
    .report-card * {
        visibility: visible !important;
        overflow: visible !important;
    }

    .report-preview {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        background: white !important;
        max-height: none !important;
        overflow: visible !important;
    }

    /* Report card specific styles - No gaps between cards */
    .report-card {
        position: relative !important;
        left: 0 !important;
        top: 0 !important;
        width: 28.1cm !important;
        height: 19.5cm !important;
        max-width: none !important;
        max-height: none !important;
        margin: 0 !important;
        padding: 8mm !important;
        box-shadow: none !important;
        border: 2px solid #000 !important;
        page-break-after: always !important;
        page-break-inside: avoid !important;
        background: white !important;
        font-family: 'Times New Roman', serif !important;
        font-size: 11px !important;
        overflow: hidden !important;
        box-sizing: border-box !important;
    }

    /* Report card header styles */
    .report-card-header h2 {
        font-size: 18px !important;
        margin: 0 !important;
    }

    .report-card-header p {
        font-size: 10px !important;
        margin: 2px 0 !important;
    }

    .report-title {
        font-size: 14px !important;
        margin: 8px 0 !important;
    }

    /* Table styles for report cards */
    .grades-table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 9px !important;
        margin: 8px 0 !important;
    }

    .grades-table th,
    .grades-table td {
        border: 1px solid #000 !important;
        padding: 3px !important;
        text-align: center !important;
        font-size: 9px !important;
        line-height: 1.2 !important;
    }

    .grades-table th {
        background-color: #e9ecef !important;
        font-weight: bold !important;
        font-size: 8px !important;
    }

    .subject-cell {
        text-align: left !important;
        font-weight: bold !important;
        font-size: 8px !important;
    }

    /* Student info tables */
    .student-info-table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 10px !important;
    }

    .student-info-table td {
        border: 1px solid #000 !important;
        padding: 4px !important;
        font-size: 10px !important;
    }

    /* Attendance and conduct sections */
    .attendance-conduct {
        display: flex !important;
        gap: 10px !important;
        margin: 8px 0 !important;
    }

    .info-table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 9px !important;
    }

    .info-table td {
        border: 1px solid #000 !important;
        padding: 3px !important;
        font-size: 9px !important;
    }

    /* Comments and signatures */
    .comments-section,
    .signatures-section {
        margin: 8px 0 !important;
        font-size: 10px !important;
    }

    .signature-box {
        text-align: center !important;
        font-size: 9px !important;
    }

    /* Grade sheet specific styles (portrait) - No gaps between sheets */
    .report-card > div[style*="21cm"] {
        width: 100% !important;
        height: 100vh !important;
        max-height: 29.7cm !important;
        margin: 0 !important;
        padding: 1cm !important;
        page-break-after: always !important;
        page-break-inside: avoid !important;
        overflow: hidden !important;
        position: relative !important;
        box-sizing: border-box !important;
    }

    /* Ensure all tables print with borders */
    table {
        border-collapse: collapse !important;
    }

    table, th, td {
        border: 1px solid #000 !important;
        print-color-adjust: exact !important;
        -webkit-print-color-adjust: exact !important;
    }

    /* Ensure background colors print */
    * {
        print-color-adjust: exact !important;
        -webkit-print-color-adjust: exact !important;
    }

    /* Force page breaks without gaps */
    .report-card > div[style*="page-break-after"] {
        page-break-after: always !important;
        page-break-inside: avoid !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Remove all spacing from grade sheet containers */
    .report-card {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        background: white !important;
        overflow: visible !important;
        page-break-inside: avoid !important;
    }

    /* Ensure no gaps between grade sheet pages */
    .report-card > div {
        margin: 0 !important;
        border: none !important;
        page-break-inside: avoid !important;
    }

    /* Hide browser elements when printing */
    .navbar, .btn, .card-header, .tab-content > .tab-pane:not(.active), .sidebar {
        display: none !important;
    }
}

/* Responsive Design Improvements */

/* Mobile devices (up to 768px) */
@media (max-width: 767.98px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .main-content {
        width: 100%;
        padding: 15px;
    }

    /* Mobile navigation toggle */
    .mobile-nav-toggle {
        display: block;
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 1051;
        background: var(--dark-color);
        color: white;
        border: none;
        padding: 10px;
        border-radius: 5px;
    }

    /* Responsive tables */
    .table-responsive {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    /* Responsive cards */
    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    /* Responsive forms */
    .form-control,
    .form-select {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Responsive buttons */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    /* Hide complex charts on mobile */
    canvas {
        max-height: 200px;
    }

    /* Responsive grade sheets */
    .report-card {
        width: 100% !important;
        min-width: auto !important;
        padding: 10px !important;
        font-size: 12px !important;
    }

    .grades-table {
        font-size: 10px !important;
    }

    .grades-table th,
    .grades-table td {
        padding: 2px !important;
    }
}

/* Tablet devices (768px to 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .sidebar {
        width: 200px;
    }

    .main-content {
        padding: 20px;
    }

    .table th,
    .table td {
        padding: 0.6rem;
        font-size: 0.9rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    /* Responsive grade sheets for tablets */
    .report-card {
        width: 100% !important;
        padding: 15px !important;
        font-size: 13px !important;
    }
}

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
    .main-content {
        padding: 30px;
    }

    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0066cc;
        --secondary-color: #006600;
        --danger-color: #cc0000;
        --text-color: #000000;
        --border-color: #000000;
    }

    .card {
        border: 2px solid var(--border-color);
    }

    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #ffffff;
        --light-color: #2c3e50;
        --border-color: #555555;
    }

    body {
        background-color: #1a1a1a;
        color: var(--text-color);
    }

    .card {
        background-color: #2c3e50;
        border-color: var(--border-color);
    }

    .table {
        color: var(--text-color);
    }

    .table th {
        background-color: #34495e;
        border-color: var(--border-color);
    }

    .table td {
        border-color: var(--border-color);
    }
}

/* Mobile navigation toggle (hidden by default) */
.mobile-nav-toggle {
    display: none;
}
