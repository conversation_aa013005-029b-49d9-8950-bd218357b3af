// Minimal html2canvas placeholder for School Management System
// This is a placeholder to prevent fetch errors
// The actual functionality will use browser's native print capabilities

window.html2canvas = function(element, options) {
    console.log('html2canvas placeholder loaded - using browser print functionality instead');
    return Promise.resolve({
        toDataURL: function() {
            return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
        },
        getContext: function() {
            return {
                drawImage: function() {},
                getImageData: function() { return { data: [] }; }
            };
        }
    });
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.html2canvas;
}
