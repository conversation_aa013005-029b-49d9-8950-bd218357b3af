<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>School Grade Management System</title>

    <!-- Bootstrap CSS - with offline fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='./libs/bootstrap.min.css';">

    <!-- Font Awesome - with offline fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
          onerror="this.onerror=null;this.href='./libs/fontawesome.min.css';">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="./style.css">
    <link rel="stylesheet" href="./report-card-styles.css">
    <link rel="stylesheet" href="./print-optimized-styles.css">

    <!-- PDF Generation Libraries - with offline fallback -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"
            onerror="document.write('<script src=\'./libs/jspdf.min.js\'><\/script>')"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"
            onerror="document.write('<script src=\'./libs/html2canvas.min.js\'><\/script>')"></script>

    <style>
        /* UPDATED 2024-12-19: Compact Attendance & Conduct Sections */
        /* Animation for fade-out effect */
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        /* Style for the current student in grading */
        #gradeStudent option.fw-bold {
            font-weight: bold;
            background-color: #e9f5ff;
        }

        /* Smooth transition for progress bar */
        #studentProgressIndicator .progress-bar {
            transition: width 0.5s ease-in-out;
        }

        /* Highlight new payment records */
        #paymentRecordsTable tr:first-child {
            animation: highlightRow 2s ease-out;
        }

        @keyframes highlightRow {
            0% { background-color: #d4edda; }
            100% { background-color: transparent; }
        }

        /* Toast notification styling */
        .alert.position-fixed {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 4px;
        }

        /* Professional Report Card Styles */
        .report-card {
            font-family: 'Times New Roman', serif;
            background: white;
            border: 2px solid #000;
            page-break-after: always;
            width: 297mm;
            min-height: 210mm;
            padding: 15mm;
            margin: 0 auto 20px auto;
            box-sizing: border-box;
            position: relative;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .report-card-header {
            text-align: center;
            border-bottom: 3px double #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .school-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
            border: 1px solid #ccc;
        }

        .school-info h2 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
            color: #000;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .school-info p {
            margin: 5px 0;
            font-size: 12px;
            color: #333;
        }

        .report-title {
            margin: 10px 0 0 0;
            font-size: 18px;
            font-weight: bold;
            color: #000;
            text-decoration: underline;
        }

        .student-photo {
            width: 80px;
            height: 80px;
            border: 2px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            text-align: center;
        }

        .student-info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .student-info-table td {
            padding: 5px;
            font-size: 14px;
        }

        .student-info-table .label {
            font-weight: bold;
            width: 120px;
        }

        .student-info-table .value {
            border-bottom: 1px solid #000;
        }

        .grading-scale {
            margin-bottom: 20px;
            border: 2px solid #000;
            padding: 10px;
            background-color: #f9f9f9;
        }

        .grading-scale h4 {
            margin: 0 0 10px 0;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
        }

        .grading-scale-content {
            display: flex;
            justify-content: space-around;
            font-size: 12px;
            font-weight: bold;
        }

        .grades-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            font-size: 10px;
            margin-bottom: 20px;
        }

        .grades-table th,
        .grades-table td {
            border: 1px solid #000;
            padding: 4px;
            text-align: center;
            vertical-align: middle;
        }

        .grades-table th {
            background-color: #e9ecef;
            font-weight: bold;
            font-size: 11px;
        }

        .grades-table .subject-cell {
            text-align: left;
            font-weight: bold;
            font-size: 10px;
        }

        .grades-table .semester-avg {
            background-color: #f0f8ff;
            font-weight: bold;
        }

        .grades-table .year-avg {
            background-color: #e6ffe6;
            font-weight: bold;
            font-size: 11px;
        }

        .grades-table .overall-row {
            background-color: #e9ecef;
            font-weight: bold;
            border: 2px solid #000;
        }

        .grades-table .failing-grade {
            color: #dc3545;
            font-weight: bold;
        }

        .attendance-conduct {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .attendance-section,
        .conduct-section {
            flex: 1;
            border: 2px solid #000;
            padding: 15px;
            margin-right: 10px;
        }

        .conduct-section {
            margin-right: 0;
            margin-left: 10px;
        }

        .section-title {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            text-decoration: underline;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .info-table td {
            padding: 5px;
        }

        .info-table .label {
            font-weight: bold;
        }

        .info-table .value {
            border-bottom: 1px solid #000;
            text-align: center;
        }

        .comments-section {
            margin-bottom: 20px;
            border: 2px solid #000;
            padding: 15px;
        }

        .comments-content {
            min-height: 60px;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #fafafa;
        }

        .promotion-section {
            margin-bottom: 20px;
            text-align: center;
            border: 2px solid #000;
            padding: 15px;
            background-color: #f0f8ff;
        }

        .promotion-section h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: bold;
        }

        .signatures-section {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            font-size: 12px;
        }

        .signature-box {
            text-align: center;
            width: 30%;
        }

        .signature-line {
            border-bottom: 2px solid #000;
            margin-bottom: 5px;
            height: 40px;
        }

        .signature-title {
            font-weight: bold;
        }

        .signature-date {
            margin-top: 10px;
        }

        .report-footer {
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }

        /* Define A4 landscape page settings */
        @page {
            size: A4 landscape;
            margin: 0.8cm;
        }

        @media print {
            /* Reset all elements */
            * {
                box-sizing: border-box !important;
            }

            /* Hide all interface elements */
            body * {
                visibility: hidden;
            }

            /* Show only report cards and grade sheets */
            .report-card,
            .report-card * {
                visibility: visible !important;
                overflow: visible !important;
            }

            /* Set up page layout */
            html, body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                width: 100% !important;
                height: 100% !important;
                font-size: 14px !important;
            }

            /* Position report cards for printing */
            .report-card {
                position: absolute !important;
                left: 0 !important;
                top: 0 !important;
                width: 28.1cm !important; /* A4 landscape width minus margins */
                height: 19.5cm !important;  /* A4 landscape height minus margins */
                max-width: none !important;
                max-height: none !important;
                margin: 0 !important;
                padding: 6mm !important;
                box-shadow: none !important;
                border: 1px solid #000 !important;
                page-break-after: always !important;
                background: white !important;
                font-family: 'Times New Roman', serif !important;
                font-size: 12px !important;
                overflow: visible !important;
            }

            /* Optimize header section for print */
            .report-card-header {
                text-align: center !important;
                border-bottom: 2px solid #000 !important;
                padding-bottom: 8px !important;
                margin-bottom: 10px !important;
                height: auto !important;
            }

            .school-info h2 {
                font-size: 18px !important;
                margin: 2px 0 !important;
                line-height: 1.2 !important;
            }

            .school-info p {
                font-size: 9px !important;
                margin: 1px 0 !important;
                line-height: 1.1 !important;
            }

            .report-title {
                font-size: 14px !important;
                margin: 5px 0 0 0 !important;
            }

            /* Optimize student info section */
            .student-info-table {
                margin-bottom: 8px !important;
            }

            .student-info-table td {
                padding: 2px !important;
                font-size: 10px !important;
                line-height: 1.1 !important;
            }

            /* Optimize grading scale */
            .grading-scale {
                margin-bottom: 8px !important;
                padding: 5px !important;
            }

            .grading-scale h4 {
                font-size: 10px !important;
                margin: 0 0 5px 0 !important;
            }

            .grading-scale-content {
                font-size: 8px !important;
            }

            /* Optimize grades table */
            .grades-table {
                border-collapse: collapse !important;
                width: 100% !important;
                margin-bottom: 12px !important;
                font-size: 11px !important;
                border: 2px solid #000 !important;
            }

            .grades-table th,
            .grades-table td {
                border: 1px solid #000 !important;
                padding: 4px !important;
                font-size: 11px !important;
                line-height: 1.2 !important;
                vertical-align: middle !important;
                text-align: center !important;
            }

            .grades-table th {
                font-size: 10px !important;
                font-weight: bold !important;
                background-color: #e9ecef !important;
            }

            .grades-table .subject-cell {
                font-size: 10px !important;
                text-align: left !important;
                font-weight: bold !important;
            }

            /* Optimize attendance and conduct section - COMPACT SIZE */
            .attendance-conduct {
                display: flex !important;
                justify-content: space-between !important;
                margin-bottom: 8px !important;
            }

            .attendance-section,
            .conduct-section {
                flex: 1 !important;
                border: 1px solid #000 !important;
                padding: 6px !important;
                margin: 0 2px !important;
            }

            .section-title {
                font-size: 10px !important;
                margin: 0 0 4px 0 !important;
                font-weight: bold !important;
                text-decoration: underline !important;
                text-align: center !important;
            }

            .info-table {
                font-size: 9px !important;
                width: 100% !important;
                border-collapse: collapse !important;
            }

            .info-table td {
                padding: 2px 3px !important;
                line-height: 1.2 !important;
            }

            .info-table .label {
                font-weight: bold !important;
                width: 65% !important;
            }

            .info-table .value {
                border-bottom: 1px solid #000 !important;
                text-align: center !important;
                font-weight: bold !important;
            }

            /* Optimize comments section */
            .comments-section {
                margin-bottom: 12px !important;
                border: 1px solid #000 !important;
                padding: 8px !important;
            }

            .comments-content {
                min-height: 35px !important;
                padding: 6px !important;
                font-size: 10px !important;
                line-height: 1.3 !important;
            }

            /* Optimize conduct recommendations - COMPACT SIZE */
            .conduct-recommendations {
                margin-top: 4px !important;
            }

            .conduct-recommendations strong {
                font-size: 9px !important;
                font-weight: bold !important;
            }

            .conduct-recommendations ul {
                margin: 2px 0 !important;
                padding-left: 10px !important;
            }

            .conduct-recommendations li {
                font-size: 8px !important;
                line-height: 1.2 !important;
                margin-bottom: 1px !important;
            }

            /* Grade Sheet Print Optimization */
            #gradeSheetPreview,
            #gradeSheetPreview * {
                visibility: visible !important;
                overflow: visible !important;
            }

            /* Grade Sheet Container - A4 Landscape Optimized */
            #gradeSheetPreview .report-card {
                width: 29.7cm !important;
                height: 21cm !important;
                padding: 0 !important;
                margin: 0 !important;
                font-size: 10px !important;
                page-break-after: always !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
            }

            /* A4 Landscape Layout Optimization */
            #gradeSheetPreview div[style*="display: flex"] {
                width: 100% !important;
                height: 19cm !important;
                padding: 0.5cm !important;
                gap: 0.5cm !important;
                margin: 0 !important;
                box-sizing: border-box !important;
            }

            #gradeSheetPreview div[style*="flex: 1"] {
                height: 100% !important;
                overflow: visible !important;
                display: flex !important;
                flex-direction: column !important;
                min-width: 0 !important;
                flex-shrink: 0 !important;
            }

            /* Grade Sheet Headers - A4 Landscape Compact */
            #gradeSheetPreview h4 {
                font-size: 11px !important;
                margin: 1px 0 !important;
                line-height: 1.1 !important;
            }

            #gradeSheetPreview h5 {
                font-size: 10px !important;
                margin: 1px 0 !important;
                line-height: 1.1 !important;
            }

            #gradeSheetPreview h6 {
                font-size: 9px !important;
                margin: 1px 0 !important;
                line-height: 1.1 !important;
            }

            #gradeSheetPreview p {
                font-size: 8px !important;
                margin: 0.5px 0 !important;
                line-height: 1.1 !important;
            }

            /* Grade Sheet Tables - A4 Landscape Optimized */
            #gradeSheetPreview table {
                border-collapse: collapse !important;
                width: 100% !important;
                font-size: 9px !important;
                border: 1px solid #000 !important;
                table-layout: auto !important;
                min-width: 100% !important;
            }

            #gradeSheetPreview table th,
            #gradeSheetPreview table td {
                border: 1px solid #000 !important;
                padding: 2px !important;
                font-size: 9px !important;
                line-height: 1.1 !important;
                white-space: nowrap !important;
                overflow: visible !important;
            }

            #gradeSheetPreview table th {
                font-weight: bold !important;
                background-color: #e9ecef !important;
                font-size: 8px !important;
                min-width: 30px !important;
            }

            /* Grade Values - Compact but readable */
            #gradeSheetPreview table td[style*="font-weight: bold"] {
                font-weight: bold !important;
                font-size: 10px !important;
            }

            /* Ensure table containers don't cut off content */
            #gradeSheetPreview div[style*="overflow: auto"] {
                overflow: visible !important;
            }

            /* Student Photos */
            #gradeSheetPreview img {
                width: 50px !important;
                height: 50px !important;
                border: 2px solid #000 !important;
            }

            /* Student Names */
            #gradeSheetPreview h5[style*="font-size: 18px"] {
                font-size: 16px !important;
                font-weight: bold !important;
            }

            /* Rank and Summary Sections */
            #gradeSheetPreview div[style*="font-size: 14px"] {
                font-size: 12px !important;
            }

            #gradeSheetPreview div[style*="font-size: 10px"] {
                font-size: 9px !important;
            }

            /* Signature Lines */
            #gradeSheetPreview div[style*="border-bottom: 2px solid #000"] {
                border-bottom: 2px solid #000 !important;
                height: 20px !important;
            }

            /* Optimize promotion section */
            .promotion-section {
                margin-bottom: 8px !important;
                border: 1px solid #000 !important;
                padding: 5px !important;
            }

            .promotion-section h4 {
                font-size: 10px !important;
                margin: 0 0 3px 0 !important;
            }

            /* Optimize signatures section */
            .signatures-section {
                display: flex !important;
                justify-content: space-between !important;
                margin-top: 8px !important;
                font-size: 8px !important;
            }

            .signature-box {
                text-align: center !important;
                width: 30% !important;
            }

            .signature-line {
                border-bottom: 2px solid #000 !important;
                margin-bottom: 4px !important;
                height: 30px !important;
                min-height: 30px !important;
            }

            .signature-date {
                margin-top: 4px !important;
                font-size: 9px !important;
            }

            /* Grade Sheet Signature Lines - Prevent Cutting */
            #gradeSheetPreview div[style*="border-bottom: 2px solid #000"] {
                height: 30px !important;
                min-height: 30px !important;
                margin-bottom: 6px !important;
                border-bottom: 2px solid #000 !important;
                page-break-inside: avoid !important;
            }

            /* Grade Sheet Signature Sections - Prevent Breaking */
            #gradeSheetPreview div[style*="justify-content: space-around"] {
                page-break-inside: avoid !important;
                margin-top: 8px !important;
                margin-bottom: 8px !important;
            }

            /* Optimize footer */
            .report-footer {
                margin-top: 5px !important;
                font-size: 7px !important;
                border-top: 1px solid #ccc !important;
                padding-top: 3px !important;
            }

            /* Hide specific interface elements */
            .navbar,
            .nav-tabs,
            .card-header,
            .btn,
            .form-control,
            .form-select,
            .alert,
            .spinner-border,
            .position-fixed,
            .no-print,
            #reportActions,
            .card:not(.report-card) {
                display: none !important;
                visibility: hidden !important;
            }

            /* Ensure report preview container is clean */
            #reportPreview {
                position: static !important;
                visibility: visible !important;
                background: white !important;
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                height: 100% !important;
            }

            /* Ensure proper page breaks */
            .report-card:last-child {
                page-break-after: avoid !important;
            }

            /* Ensure text is black for printing */
            .report-card,
            .report-card * {
                color: black !important;
                background: white !important;
            }

            /* Preserve failing grade indicators */
            .failing-grade {
                color: #000 !important;
                font-weight: bold !important;
                text-decoration: underline !important;
            }

            /* Hide Bootstrap components */
            .container,
            .container-fluid,
            .tab-content,
            .tab-pane {
                width: 100% !important;
                max-width: none !important;
                padding: 0 !important;
                margin: 0 !important;
                border: none !important;
            }

            /* Ensure only reports tab is visible */
            .tab-pane:not(#reports) {
                display: none !important;
            }
        }

        /* Application Form Styles */
        .section-header {
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .section-header h6 {
            font-weight: 600;
            color: #0d6efd !important;
        }

        .photo-upload-container {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            transition: border-color 0.3s ease;
        }

        .photo-upload-container:hover {
            border-color: #0d6efd;
        }

        .photo-placeholder {
            padding: 40px 20px;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 6px;
        }

        .photo-preview img {
            border: 3px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .form-label {
            font-weight: 500;
            color: #495057;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        .admin-only {
            border-left: 4px solid #ffc107;
        }

        .application-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .application-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .student-photo-thumb {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid #dee2e6;
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .section-header h6 {
                font-size: 1rem;
            }

            .photo-placeholder {
                padding: 20px 10px;
            }

            .photo-placeholder i {
                font-size: 3rem !important;
            }
        }
    </style>
</head>
<body>
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="sr-only sr-only-focusable btn btn-primary position-absolute" style="top: 10px; left: 10px; z-index: 9999;">
        Skip to main content
    </a>

    <!-- Mobile navigation toggle -->
    <button class="mobile-nav-toggle" id="mobileNavToggle" aria-label="Toggle navigation menu" aria-expanded="false">
        <i class="fas fa-bars" aria-hidden="true"></i>
    </button>

    <div class="container-fluid">
        <!-- Sidebar Navigation -->
        <div class="row">
            <nav class="col-md-2 sidebar" role="navigation" aria-label="Main navigation">
                <div class="sidebar-header">
                    <h3>School System</h3>
                    <div class="user-info mt-3">
                        <div class="d-flex align-items-center">
                            <div class="user-avatar" aria-hidden="true">
                                <i class="fas fa-user-circle fa-2x text-light"></i>
                            </div>
                            <div class="user-details ms-2">
                                <h6 class="user-name mb-0 text-light" id="userFullName">Guest</h6>
                                <small class="user-role text-light-50" id="userRole">Not logged in</small>
                            </div>
                        </div>
                        <button id="logoutBtn" class="btn btn-sm btn-outline-light mt-2 w-100" aria-label="Logout from system">
                            <i class="fas fa-sign-out-alt me-1" aria-hidden="true"></i> Logout
                        </button>
                    </div>
                </div>
                <ul class="nav flex-column" id="mainNav" role="menubar">
                    <li class="nav-item" role="none">
                        <a class="nav-link active" data-bs-toggle="tab" href="#dashboard" role="menuitem" aria-current="page" aria-label="Dashboard - View system overview">
                            <i class="fas fa-tachometer-alt" aria-hidden="true"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item teacher-admin-only" role="none">
                        <a class="nav-link" data-bs-toggle="tab" href="#enterGrades" role="menuitem" aria-label="Enter Grades - Input student grades">
                            <i class="fas fa-edit" aria-hidden="true"></i> Enter Grades
                        </a>
                    </li>
                    <li class="nav-item teacher-admin-only" role="none">
                        <a class="nav-link" data-bs-toggle="tab" href="#advancedGradebook" role="menuitem" aria-label="Advanced Gradebook - Detailed grade management">
                            <i class="fas fa-chart-line" aria-hidden="true"></i> Advanced Gradebook
                        </a>
                    </li>
                    <li class="nav-item admin-only" role="none">
                        <a class="nav-link" data-bs-toggle="tab" href="#addStudent" role="menuitem" aria-label="Add Student - Register new students">
                            <i class="fas fa-user-plus" aria-hidden="true"></i> Add Student
                        </a>
                    </li>
                    <li class="nav-item admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#manageStudents">
                            <i class="fas fa-users-cog"></i> Manage Students
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#reports">
                            <i class="fas fa-file-alt"></i> Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#application">
                            <i class="fas fa-user-plus"></i> Application
                        </a>
                    </li>
                    <li class="nav-item admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#feesManagement">
                            <i class="fas fa-money-bill-wave"></i> Fees Management
                        </a>
                    </li>
                    <li class="nav-item admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#parentManagement">
                            <i class="fas fa-user-friends"></i> Parent Management
                        </a>
                    </li>
                    <li class="nav-item admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#teacherManagement">
                            <i class="fas fa-chalkboard-teacher"></i> Teacher Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#attendance">
                            <i class="fas fa-calendar-check"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item teacher-admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#behavior">
                            <i class="fas fa-clipboard-list"></i> Behavior & Discipline
                        </a>
                    </li>
                    <li class="nav-item admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#staffManagement">
                            <i class="fas fa-users-cog"></i> Staff Management & HR
                        </a>
                    </li>
                    <li class="nav-item teacher-admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#studentEngagement">
                            <i class="fas fa-users"></i> Student Engagement
                        </a>
                    </li>
                    <li class="nav-item teacher-admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#curriculum">
                            <i class="fas fa-book"></i> Curriculum
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#calendar">
                            <i class="fas fa-calendar-alt"></i> Calendar
                        </a>
                    </li>
                    <li class="nav-item admin-only">
                        <a class="nav-link" data-bs-toggle="tab" href="#systemSettings">
                            <i class="fas fa-cogs"></i> System Settings
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content Area -->
            <main class="col-md-10 main-content" id="main-content" role="main" aria-label="Main content area">
                <div class="tab-content" role="tabpanel">
                    <!-- Dashboard Tab -->
                    <div class="tab-pane fade show active" id="dashboard">
                        <h2>Dashboard</h2>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Total Students</h5>
                                    </div>
                                    <div class="card-body">
                                        <h2 id="totalStudentsCount">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Total Classes</h5>
                                    </div>
                                    <div class="card-body">
                                        <h2 id="totalClassesCount">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Total Teachers</h5>
                                    </div>
                                    <div class="card-body">
                                        <h2 id="totalTeachersCount">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Parent-Student Links</h5>
                                    </div>
                                    <div class="card-body">
                                        <h2 id="totalRelationshipsCount">0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Class Summary</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="classSummaryChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Student Performance</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="studentPerformanceChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Student Performance Summary Section -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-chart-line me-2"></i>Student Performance Summary</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="card bg-success text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Excellent (80%+)</h6>
                                                        <h2 id="excellentStudentsCount">0</h2>
                                                        <button class="btn btn-light btn-sm mt-2" onclick="showPerformanceDetails('excellent')">
                                                            <i class="fas fa-eye me-1"></i>View Students
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-primary text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Good (70%-79%)</h6>
                                                        <h2 id="goodStudentsCount">0</h2>
                                                        <button class="btn btn-light btn-sm mt-2" onclick="showPerformanceDetails('good')">
                                                            <i class="fas fa-eye me-1"></i>View Students
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-warning text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Satisfactory (60%-69%)</h6>
                                                        <h2 id="satisfactoryStudentsCount">0</h2>
                                                        <button class="btn btn-light btn-sm mt-2" onclick="showPerformanceDetails('satisfactory')">
                                                            <i class="fas fa-eye me-1"></i>View Students
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-danger text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Needs Improvement (&lt;60%)</h6>
                                                        <h2 id="needsImprovementStudentsCount">0</h2>
                                                        <button class="btn btn-light btn-sm mt-2" onclick="showPerformanceDetails('needsImprovement')">
                                                            <i class="fas fa-eye me-1"></i>View Students
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Student Conduct Reports Dashboard -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5><i class="fas fa-clipboard-check me-2"></i>Student Conduct Reports Dashboard</h5>
                                        <div>
                                            <button class="btn btn-sm btn-info" onclick="refreshConductDashboard()">
                                                <i class="fas fa-sync-alt me-1"></i>Refresh
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="printAllConductReports()">
                                                <i class="fas fa-print me-1"></i>Print All Reports
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!-- Conduct Summary Stats -->
                                        <div class="row mb-4">
                                            <div class="col-md-3">
                                                <div class="card bg-warning text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Students with Minor Incidents</h6>
                                                        <h2 id="studentsWithMinorCount">0</h2>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-info text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Students with Moderate Incidents</h6>
                                                        <h2 id="studentsWithModerateCount">0</h2>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-danger text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Students with Major Incidents</h6>
                                                        <h2 id="studentsWithMajorCount">0</h2>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-dark text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Students with Severe Incidents</h6>
                                                        <h2 id="studentsWithSevereCount">0</h2>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Student Conduct List -->
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th>Student</th>
                                                        <th>Class</th>
                                                        <th class="text-center">Minor</th>
                                                        <th class="text-center">Moderate</th>
                                                        <th class="text-center">Major</th>
                                                        <th class="text-center">Severe</th>
                                                        <th class="text-center">Conduct Score</th>
                                                        <th class="text-center">Grade</th>
                                                        <th class="text-center">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="conductDashboardTable">
                                                    <tr>
                                                        <td colspan="9" class="text-center text-muted">
                                                            <i class="fas fa-spinner fa-spin me-2"></i>Loading conduct data...
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Application Overview Section -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5><i class="fas fa-file-alt me-2"></i>Application Overview</h5>
                                        <a href="#application" class="btn btn-sm btn-primary" onclick="showTab('application')">
                                            <i class="fas fa-plus me-1"></i>New Application
                                        </a>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="card bg-info text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Total Applications</h6>
                                                        <h2 id="totalApplicationsCount">0</h2>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-warning text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Pending</h6>
                                                        <h2 id="pendingApplicationsCount">0</h2>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-success text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Approved</h6>
                                                        <h2 id="approvedApplicationsCount">0</h2>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="card bg-secondary text-white">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Converted to Students</h6>
                                                        <h2 id="convertedApplicationsCount">0</h2>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Fee Summary Section -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5><i class="fas fa-dollar-sign me-2"></i>Fee Summary</h5>
                                        <a href="#" class="btn btn-sm btn-primary" id="viewFeesDetailBtn">View Details</a>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="card bg-light">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Students Owing Fees</h6>
                                                        <h2 id="studentsOwingCount" class="text-danger">0</h2>
                                                        <small class="text-muted">Outstanding Balances</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="card bg-light">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Total Outstanding Fees</h6>
                                                        <h2 id="totalFeesOwed" class="text-danger">$0.00</h2>
                                                        <small class="text-muted">Amount Owed</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="card bg-light">
                                                    <div class="card-body text-center">
                                                        <h6 class="card-title">Total Fees Collected</h6>
                                                        <h2 id="totalFeesCollected" class="text-success">$0.00</h2>
                                                        <small class="text-muted">Payments Received</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enter Grades Tab -->
                    <div class="tab-pane fade" id="enterGrades">
                        <h2>Enter Grades</h2>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>School Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="schoolInfoForm">
                                            <div class="mb-3">
                                                <label for="schoolName" class="form-label">School Name</label>
                                                <input type="text" class="form-control" id="schoolName">
                                            </div>
                                            <div class="mb-3">
                                                <label for="schoolAddress" class="form-label">Address</label>
                                                <textarea class="form-control" id="schoolAddress" rows="2"></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label for="schoolEmail" class="form-label">Email (Optional)</label>
                                                <input type="email" class="form-control" id="schoolEmail">
                                            </div>
                                            <div class="mb-3">
                                                <label for="schoolLogo" class="form-label">School Logo</label>
                                                <input type="file" class="form-control" id="schoolLogo" accept="image/*">
                                                <div id="currentLogoPreview" class="mt-2" style="display: none;">
                                                    <p>Current Logo:</p>
                                                    <img id="logoPreview" src="" alt="School Logo" style="max-width: 100px; max-height: 100px;">
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="schoolPhone" class="form-label">Phone Number (Optional)</label>
                                                <input type="tel" class="form-control" id="schoolPhone">
                                            </div>
                                            <div class="mb-3">
                                                <label for="schoolWebsite" class="form-label">Website (Optional)</label>
                                                <input type="url" class="form-control" id="schoolWebsite">
                                            </div>
                                            <div class="mb-3">
                                                <label for="academicYear" class="form-label">Academic Year</label>
                                                <input type="text" class="form-control" id="academicYear">
                                            </div>
                                            <div class="mb-3">
                                                <label for="schoolDays" class="form-label">Number of School Days</label>
                                                <input type="number" class="form-control" id="schoolDays">
                                            </div>
                                            <div class="mb-3">
                                                <label for="principal" class="form-label">Principal</label>
                                                <input type="text" class="form-control" id="principal">
                                            </div>
                                            <div class="mb-3">
                                                <label for="classSponsor" class="form-label">Class Sponsor</label>
                                                <input type="text" class="form-control" id="classSponsor">
                                            </div>
                                            <button type="submit" class="btn btn-primary">Save Information</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Grade Entry Form</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="gradeEntryForm">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gradeClass" class="form-label">Class</label>
                                                        <select class="form-select" id="gradeClass">
                                                            <option value="">Select Class</option>
                                                            <option value="Grade One">Grade One</option>
                                                            <option value="Grade Two">Grade Two</option>
                                                            <option value="Grade Three">Grade Three</option>
                                                            <option value="Grade Four">Grade Four</option>
                                                            <option value="Grade Five">Grade Five</option>
                                                            <option value="Grade Six">Grade Six</option>
                                                            <option value="Grade Seven">Grade Seven</option>
                                                            <option value="Grade Eight">Grade Eight</option>
                                                            <option value="Grade Nine">Grade Nine</option>
                                                            <option value="Grade Ten">Grade Ten</option>
                                                            <option value="Grade Eleven">Grade Eleven</option>
                                                            <option value="Grade Twelve">Grade Twelve</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gradeStudent" class="form-label">Student</label>
                                                        <select class="form-select" id="gradeStudent">
                                                            <option value="">Select Student</option>
                                                            <!-- Students will be dynamically added here -->
                                                        </select>
                                                        <div id="studentProgressIndicator" class="progress mt-2" style="display: none; height: 5px;">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                                        </div>
                                                        <small id="studentCounter" class="text-muted mt-1" style="display: none;">Student 0 of 0</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gradeSubject" class="form-label">Subject</label>
                                                        <select class="form-select" id="gradeSubject">
                                                            <option value="">Select Subject</option>
                                                            <!-- Subjects will be dynamically added here -->
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gradePeriod" class="form-label">Period</label>
                                                        <select class="form-select" id="gradePeriod">
                                                            <option value="">Select Period</option>
                                                            <option value="1ST P">1ST P</option>
                                                            <option value="2ND P">2ND P</option>
                                                            <option value="3RD P">3RD P</option>
                                                            <option value="EXAM 1">EXAM 1</option>
                                                            <option value="AV">AV</option>
                                                            <option value="4TH P">4TH P</option>
                                                            <option value="5TH P">5TH P</option>
                                                            <option value="6TH P">6TH P</option>
                                                            <option value="EXAM 2">EXAM 2</option>
                                                            <option value="AVE">AVE</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gradeValue" class="form-label">Grade</label>
                                                        <input type="number" class="form-control" id="gradeValue" min="0" max="100">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gradeComment" class="form-label">Comment (Optional)</label>
                                                        <textarea class="form-control" id="gradeComment" rows="2"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <button type="submit" class="btn btn-primary">Enter Grade / Comment</button>
                                                <button type="button" class="btn btn-link" data-bs-toggle="collapse" data-bs-target="#keyboardShortcutsHelp">
                                                    <i class="fas fa-keyboard me-1"></i> Keyboard Shortcuts
                                                </button>
                                            </div>

                                            <div class="collapse mt-3" id="keyboardShortcutsHelp">
                                                <div class="card card-body bg-light">
                                                    <h6 class="card-title"><i class="fas fa-keyboard me-2"></i>Keyboard Shortcuts for Fast Grading</h6>
                                                    <ul class="mb-0 ps-3">
                                                        <li><strong>Enter</strong> - Save grade and move to next student</li>
                                                        <li><strong>Shift+Enter</strong> - Skip to next student without saving</li>
                                                        <li><strong>Tab</strong> - Move between grade and comment fields</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Gradebook Tab -->
                    <div class="tab-pane fade" id="advancedGradebook">
                        <div class="container-fluid">
                            <h2 class="mb-4"><i class="fas fa-chart-line me-2"></i>Advanced Gradebook & Assessment</h2>

                            <!-- Weighted Grading System Overview -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-balance-scale me-2"></i>Weighted Grading System</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 border rounded bg-primary text-white">
                                                        <h4>40%</h4>
                                                        <p class="mb-0">Tests</p>
                                                        <small>40 points</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 border rounded bg-info text-white">
                                                        <h4>40%</h4>
                                                        <p class="mb-0">Quizzes</p>
                                                        <small>40 points</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 border rounded bg-success text-white">
                                                        <h4>10%</h4>
                                                        <p class="mb-0">Class Participation</p>
                                                        <small>10 points</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 border rounded bg-warning text-white">
                                                        <h4>10%</h4>
                                                        <p class="mb-0">Assignments</p>
                                                        <small>10 points</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Assessment Management -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-plus-circle me-2"></i>Create Assessment</h5>
                                        </div>
                                        <div class="card-body">
                                            <form id="assessmentForm">
                                                <div class="mb-3">
                                                    <label for="assessmentClass" class="form-label">Class *</label>
                                                    <select class="form-select" id="assessmentClass" required onchange="handleAssessmentClassChange()">
                                                        <option value="">Select Class</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="assessmentSubject" class="form-label">Subject *</label>
                                                    <select class="form-select" id="assessmentSubject" required>
                                                        <option value="">Select Subject</option>
                                                    </select>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="assessmentType" class="form-label">Assessment Type *</label>
                                                            <select class="form-select" id="assessmentType" required onchange="updateAssessmentWeights()">
                                                                <option value="">Select Type</option>
                                                                <option value="Test">Test (40%)</option>
                                                                <option value="Quiz">Quiz (40%)</option>
                                                                <option value="Assignment">Assignment (10%)</option>
                                                                <option value="Participation">Class Participation (10%)</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="assessmentMaxPoints" class="form-label">Max Points *</label>
                                                            <input type="number" class="form-control" id="assessmentMaxPoints" required min="1" max="100">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="assessmentTitle" class="form-label">Assessment Title *</label>
                                                    <input type="text" class="form-control" id="assessmentTitle" required
                                                           placeholder="e.g., Chapter 5 Test, Math Quiz 1">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="assessmentDate" class="form-label">Assessment Date *</label>
                                                    <input type="date" class="form-control" id="assessmentDate" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="assessmentDescription" class="form-label">Description</label>
                                                    <textarea class="form-control" id="assessmentDescription" rows="2"
                                                              placeholder="Optional description or notes..."></textarea>
                                                </div>
                                                <button type="button" class="btn btn-primary" onclick="createAssessment()">
                                                    <i class="fas fa-save me-2"></i>Create Assessment
                                                </button>
                                                <button type="button" class="btn btn-secondary ms-2" onclick="clearAssessmentForm()">
                                                    <i class="fas fa-times me-2"></i>Clear
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-chart-bar me-2"></i>Grade Analytics</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="analyticsClass" class="form-label">Select Class</label>
                                                <select class="form-select" id="analyticsClass" onchange="updateGradeAnalytics()">
                                                    <option value="">Select Class</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="analyticsSubject" class="form-label">Select Subject</label>
                                                <select class="form-select" id="analyticsSubject" onchange="updateGradeAnalytics()">
                                                    <option value="">Select Subject</option>
                                                </select>
                                            </div>
                                            <div id="gradeAnalyticsDisplay">
                                                <p class="text-muted text-center">Select class and subject to view analytics</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Student Grade Breakdown by Period -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5><i class="fas fa-user-graduate me-2"></i>Student Grade Breakdown by Period</h5>
                                            <div>
                                                <button class="btn btn-outline-primary btn-sm" onclick="refreshStudentGradeBreakdown()">
                                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                                </button>
                                                <button class="btn btn-outline-success btn-sm" onclick="exportStudentGrades()">
                                                    <i class="fas fa-download me-1"></i>Export
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-3">
                                                    <label for="gradeBreakdownClass" class="form-label">Select Class</label>
                                                    <select class="form-select" id="gradeBreakdownClass" onchange="updateGradeBreakdownSubjects()">
                                                        <option value="">Select Class</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="gradeBreakdownSubject" class="form-label">Select Subject</label>
                                                    <select class="form-select" id="gradeBreakdownSubject" onchange="updateStudentGradeBreakdown()">
                                                        <option value="">Select Subject</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="gradeBreakdownPeriod" class="form-label">Select Period</label>
                                                    <select class="form-select" id="gradeBreakdownPeriod" onchange="updateStudentGradeBreakdown()">
                                                        <option value="">All Periods</option>
                                                        <option value="1st Pd.">1st Pd.</option>
                                                        <option value="2nd Pd.">2nd Pd.</option>
                                                        <option value="3rd Pd.">3rd Pd.</option>
                                                        <option value="Exam 1">Exam 1</option>
                                                        <option value="4th Pd.">4th Pd.</option>
                                                        <option value="5th Pd.">5th Pd.</option>
                                                        <option value="6th Pd.">6th Pd.</option>
                                                        <option value="Exam 2">Exam 2</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="gradeBreakdownStudent" class="form-label">Select Student (Optional)</label>
                                                    <select class="form-select" id="gradeBreakdownStudent" onchange="updateStudentGradeBreakdown()">
                                                        <option value="">All Students</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div id="studentGradeBreakdownDisplay">
                                                <div class="text-center text-muted py-4">
                                                    <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                                    <p>Select class and subject to view student grade breakdown</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Assessment List and Grade Entry -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5><i class="fas fa-list me-2"></i>Assessments & Grade Entry</h5>
                                            <div>
                                                <button class="btn btn-outline-primary btn-sm" onclick="refreshAssessmentsList()">
                                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                                </button>
                                                <button class="btn btn-outline-success btn-sm" onclick="showBulkGradeImport()">
                                                    <i class="fas fa-upload me-1"></i>Import Grades
                                                </button>
                                                <button class="btn btn-outline-info btn-sm" onclick="exportGradebook()">
                                                    <i class="fas fa-download me-1"></i>Export
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-4">
                                                    <select class="form-select" id="filterAssessmentClass" onchange="filterAssessments()">
                                                        <option value="">All Classes</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-4">
                                                    <select class="form-select" id="filterAssessmentSubject" onchange="filterAssessments()">
                                                        <option value="">All Subjects</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-4">
                                                    <select class="form-select" id="filterAssessmentType" onchange="filterAssessments()">
                                                        <option value="">All Types</option>
                                                        <option value="Test">Tests</option>
                                                        <option value="Quiz">Quizzes</option>
                                                        <option value="Assignment">Assignments</option>
                                                        <option value="Participation">Participation</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Assessment</th>
                                                            <th>Class/Subject</th>
                                                            <th>Type</th>
                                                            <th>Date</th>
                                                            <th>Max Points</th>
                                                            <th>Graded</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="assessmentsTable">
                                                        <tr>
                                                            <td colspan="7" class="text-center text-muted">
                                                                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                                                                <p>No assessments found. Create your first assessment above.</p>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Student Tab -->
                    <div class="tab-pane fade" id="addStudent">
                        <h2>Add Student</h2>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Student Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="addStudentForm" enctype="multipart/form-data">
                                            <div class="mb-3">
                                                <label for="studentName" class="form-label">Full Name</label>
                                                <input type="text" class="form-control" id="studentName" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="studentClass" class="form-label">Assign to Class</label>
                                                <select class="form-select" id="studentClass" required>
                                                    <option value="">Select Class</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>

                                            <!-- Photo Upload Section -->
                                            <div class="mb-3">
                                                <label for="addStudentPhoto" class="form-label">Student Photo</label>
                                                <div class="photo-upload-container">
                                                    <div class="photo-preview" id="addStudentPhotoPreview" style="display: none;">
                                                        <img id="addStudentPhotoImg" src="" alt="Student Photo" class="img-fluid" style="max-width: 150px; max-height: 150px;">
                                                    </div>
                                                    <div class="photo-placeholder" id="addStudentPhotoPlaceholder">
                                                        <i class="fas fa-camera fa-2x text-muted mb-2"></i>
                                                        <p class="text-muted mb-0">Click to upload student photo</p>
                                                        <small class="text-muted">JPG, PNG, GIF (Max 5MB)</small>
                                                    </div>
                                                    <input type="file" class="form-control mt-2" id="addStudentPhoto" name="student_photo" accept="image/*" onchange="previewAddStudentPhoto(this)">
                                                    <button type="button" class="btn btn-outline-danger btn-sm mt-2" id="removeAddStudentPhotoBtn" onclick="removeAddStudentPhoto()" style="display: none;">
                                                        <i class="fas fa-trash me-1"></i>Remove Photo
                                                    </button>
                                                </div>
                                            </div>

                                            <button type="submit" class="btn btn-primary">Add Student</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Recently Added Students</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-group" id="recentStudentsList">
                                            <!-- Recently added students will be dynamically added here -->
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Manage Students Tab -->
                    <div class="tab-pane fade" id="manageStudents">
                        <h2>Manage Students</h2>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Select Student</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="manageStudentClass" class="form-label">Class</label>
                                            <select class="form-select" id="manageStudentClass">
                                                <option value="">Select Class</option>
                                                <option value="Grade One">Grade One</option>
                                                <option value="Grade Two">Grade Two</option>
                                                <option value="Grade Three">Grade Three</option>
                                                <option value="Grade Four">Grade Four</option>
                                                <option value="Grade Five">Grade Five</option>
                                                <option value="Grade Six">Grade Six</option>
                                                <option value="Grade Seven">Grade Seven</option>
                                                <option value="Grade Eight">Grade Eight</option>
                                                <option value="Grade Nine">Grade Nine</option>
                                                <option value="Grade Ten">Grade Ten</option>
                                                <option value="Grade Eleven">Grade Eleven</option>
                                                <option value="Grade Twelve">Grade Twelve</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="manageStudentSelect" class="form-label">Student</label>
                                            <select class="form-select" id="manageStudentSelect">
                                                <option value="">Select Student</option>
                                                <!-- Students will be dynamically added here -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Edit Student Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="editStudentForm">
                                            <div class="mb-3">
                                                <label for="editStudentName" class="form-label">Full Name</label>
                                                <input type="text" class="form-control" id="editStudentName">
                                            </div>
                                            <div class="mb-3">
                                                <label for="editStudentClass" class="form-label">Class</label>
                                                <select class="form-select" id="editStudentClass">
                                                    <option value="">Select Class</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <button type="submit" class="btn btn-primary">Save Changes</button>
                                                <button type="button" class="btn btn-danger" id="removeStudentBtn">Remove Student</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Students List Table -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>All Students</h5>
                                        <div>
                                            <button class="btn btn-sm btn-light" onclick="refreshStudentsList()">
                                                <i class="fas fa-sync-alt me-1"></i>Refresh
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="exportStudents()">
                                                <i class="fas fa-file-excel me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!-- Search and Filter -->
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <input type="text" class="form-control" id="studentSearch" placeholder="Search by name or ID...">
                                            </div>
                                            <div class="col-md-3">
                                                <select class="form-select" id="studentClassFilter">
                                                    <option value="">All Classes</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <select class="form-select" id="studentStatusFilter">
                                                    <option value="">All Status</option>
                                                    <option value="Active">Active</option>
                                                    <option value="Inactive">Inactive</option>
                                                    <option value="Graduated">Graduated</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-primary w-100" onclick="filterStudents()">
                                                    <i class="fas fa-filter me-1"></i>Filter
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Students Table -->
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th>Photo</th>
                                                        <th>Student ID</th>
                                                        <th>Full Name</th>
                                                        <th>Class</th>
                                                        <th>Date of Birth</th>
                                                        <th>Emergency Contact</th>
                                                        <th>Status</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="studentsTableBody">
                                                    <tr>
                                                        <td colspan="8" class="text-center text-muted py-4">
                                                            <i class="fas fa-users fa-2x mb-2"></i>
                                                            <p>No students found</p>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Reports Tab -->
                    <div class="tab-pane fade" id="reports">
                        <h2>Reports</h2>

                        <!-- Report Card Generation Section -->
                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Individual Report Card</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="individualReportForm">
                                            <div class="mb-3">
                                                <label for="individualClass" class="form-label">Select Class</label>
                                                <select class="form-select" id="individualClass" required>
                                                    <option value="">Choose Class</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="individualStudent" class="form-label">Select Student</label>
                                                <select class="form-select" id="individualStudent" required>
                                                    <option value="">Choose Student</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="reportTerm" class="form-label">Report Term</label>
                                                <select class="form-select" id="reportTerm" required>
                                                    <option value="First Semester">First Semester</option>
                                                    <option value="Second Semester">Second Semester</option>
                                                    <option value="Full Year" selected>Full Year Report</option>
                                                </select>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-file-alt me-2"></i>Generate Report Card
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Class Report Cards</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="classReportForm">
                                            <div class="mb-3">
                                                <label for="classReportClass" class="form-label">Select Class</label>
                                                <select class="form-select" id="classReportClass" required>
                                                    <option value="">Choose Class</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="classReportTerm" class="form-label">Report Term</label>
                                                <select class="form-select" id="classReportTerm" required>
                                                    <option value="First Semester">First Semester</option>
                                                    <option value="Second Semester">Second Semester</option>
                                                    <option value="Full Year" selected>Full Year Report</option>
                                                </select>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-users me-2"></i>Generate All Students in Class
                                                </button>
                                                <small class="text-muted mt-2">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    Generates report cards for ALL students in the selected class
                                                </small>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Class Grade Sheets</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="gradeSheetForm">
                                            <div class="mb-3">
                                                <label for="gradeSheetClass" class="form-label">Select Class</label>
                                                <select class="form-select" id="gradeSheetClass" required>
                                                    <option value="">Choose Class</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label for="gradeSheetPeriod" class="form-label">Select Period(s)</label>
                                                <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" value="ALL" id="periodALL" onchange="handlePeriodAllChange()">
                                                        <label class="form-check-label" for="periodALL">
                                                            <strong>All Periods</strong>
                                                        </label>
                                                    </div>
                                                    <hr class="my-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input period-checkbox" type="checkbox" value="1ST P" id="period1ST" onchange="handleIndividualPeriodChange()">
                                                        <label class="form-check-label" for="period1ST">1st Period</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input period-checkbox" type="checkbox" value="2ND P" id="period2ND" onchange="handleIndividualPeriodChange()">
                                                        <label class="form-check-label" for="period2ND">2nd Period</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input period-checkbox" type="checkbox" value="3RD P" id="period3RD" onchange="handleIndividualPeriodChange()">
                                                        <label class="form-check-label" for="period3RD">3rd Period</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input period-checkbox" type="checkbox" value="4TH P" id="period4TH" onchange="handleIndividualPeriodChange()">
                                                        <label class="form-check-label" for="period4TH">4th Period</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input period-checkbox" type="checkbox" value="5TH P" id="period5TH" onchange="handleIndividualPeriodChange()">
                                                        <label class="form-check-label" for="period5TH">5th Period</label>
                                                    </div>
                                                </div>
                                                <small class="text-muted">Select one or more periods to include in the grade sheet</small>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-info">
                                                    <i class="fas fa-table me-2"></i>Generate Grade Sheet
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Individual Grade Sheet</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="individualGradeSheetForm">
                                            <div class="mb-3">
                                                <label for="individualGradeSheetClass" class="form-label">Select Class</label>
                                                <select class="form-select" id="individualGradeSheetClass" required onchange="handleIndividualGradeSheetClassChange()">
                                                    <option value="">Choose Class</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label for="individualGradeSheetStudent" class="form-label">Select Student</label>
                                                <select class="form-select" id="individualGradeSheetStudent" required>
                                                    <option value="">Choose Student</option>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label for="individualGradeSheetPeriod" class="form-label">Select Period(s)</label>
                                                <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" value="ALL" id="individualPeriodALL" onchange="handleIndividualPeriodAllChange()">
                                                        <label class="form-check-label" for="individualPeriodALL">
                                                            <strong>All Periods</strong>
                                                        </label>
                                                    </div>
                                                    <hr class="my-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input individual-period-checkbox" type="checkbox" value="1ST P" id="individualPeriod1ST" onchange="handleIndividualPeriodCheckboxChange()">
                                                        <label class="form-check-label" for="individualPeriod1ST">1st Period</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input individual-period-checkbox" type="checkbox" value="2ND P" id="individualPeriod2ND" onchange="handleIndividualPeriodCheckboxChange()">
                                                        <label class="form-check-label" for="individualPeriod2ND">2nd Period</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input individual-period-checkbox" type="checkbox" value="3RD P" id="individualPeriod3RD" onchange="handleIndividualPeriodCheckboxChange()">
                                                        <label class="form-check-label" for="individualPeriod3RD">3rd Period</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input individual-period-checkbox" type="checkbox" value="4TH P" id="individualPeriod4TH" onchange="handleIndividualPeriodCheckboxChange()">
                                                        <label class="form-check-label" for="individualPeriod4TH">4th Period</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input individual-period-checkbox" type="checkbox" value="5TH P" id="individualPeriod5TH" onchange="handleIndividualPeriodCheckboxChange()">
                                                        <label class="form-check-label" for="individualPeriod5TH">5th Period</label>
                                                    </div>
                                                </div>
                                                <small class="text-muted">Select one or more periods to include in the grade sheet</small>
                                            </div>
                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-user-graduate me-2"></i>Generate Individual Grade Sheet
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4" id="reportActions" style="display: none;">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Report Actions</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex gap-2 flex-wrap">
                                            <button type="button" class="btn btn-outline-primary" id="printReportBtn">
                                                <i class="fas fa-print me-2"></i>Print Reports
                                            </button>
                                            <button type="button" class="btn btn-outline-success" id="downloadPdfBtn">
                                                <i class="fas fa-file-pdf me-2"></i>Download PDF
                                            </button>
                                            <button type="button" class="btn btn-outline-info" id="exportExcelBtn">
                                                <i class="fas fa-file-excel me-2"></i>Export to Excel
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" id="clearReportsBtn">
                                                <i class="fas fa-trash me-2"></i>Clear Reports
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Report Preview Section -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-eye me-2"></i>Report Preview</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="reportPreview" class="report-preview">
                                            <div class="text-center text-muted py-5">
                                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                                <p>Select a class and generate reports to see preview here</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Application Tab -->
                    <div class="tab-pane fade" id="application">
                        <h2><i class="fas fa-user-plus me-2"></i>Bridge of Hope Girls' School - Application Form</h2>

                        <div class="row">
                            <!-- Application Form -->
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Student Application & Registration Form</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="applicationForm" enctype="multipart/form-data">
                                            <!-- Personal Information Section -->
                                            <div class="section-header">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-user me-2"></i>Personal Information
                                                </h6>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" id="lastName" name="last_name" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="middleName" class="form-label">Middle Name</label>
                                                        <input type="text" class="form-control" id="middleName" name="middle_name">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" id="firstName" name="first_name" required>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Date of Birth -->
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="dobDay" class="form-label">Birth Day <span class="text-danger">*</span></label>
                                                        <select class="form-select" id="dobDay" name="dob_day" required>
                                                            <option value="">Day</option>
                                                            <option value="1">1</option>
                                                            <option value="2">2</option>
                                                            <option value="3">3</option>
                                                            <option value="4">4</option>
                                                            <option value="5">5</option>
                                                            <option value="6">6</option>
                                                            <option value="7">7</option>
                                                            <option value="8">8</option>
                                                            <option value="9">9</option>
                                                            <option value="10">10</option>
                                                            <option value="11">11</option>
                                                            <option value="12">12</option>
                                                            <option value="13">13</option>
                                                            <option value="14">14</option>
                                                            <option value="15">15</option>
                                                            <option value="16">16</option>
                                                            <option value="17">17</option>
                                                            <option value="18">18</option>
                                                            <option value="19">19</option>
                                                            <option value="20">20</option>
                                                            <option value="21">21</option>
                                                            <option value="22">22</option>
                                                            <option value="23">23</option>
                                                            <option value="24">24</option>
                                                            <option value="25">25</option>
                                                            <option value="26">26</option>
                                                            <option value="27">27</option>
                                                            <option value="28">28</option>
                                                            <option value="29">29</option>
                                                            <option value="30">30</option>
                                                            <option value="31">31</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="dobMonth" class="form-label">Birth Month <span class="text-danger">*</span></label>
                                                        <select class="form-select" id="dobMonth" name="dob_month" required>
                                                            <option value="">Month</option>
                                                            <option value="1">January</option>
                                                            <option value="2">February</option>
                                                            <option value="3">March</option>
                                                            <option value="4">April</option>
                                                            <option value="5">May</option>
                                                            <option value="6">June</option>
                                                            <option value="7">July</option>
                                                            <option value="8">August</option>
                                                            <option value="9">September</option>
                                                            <option value="10">October</option>
                                                            <option value="11">November</option>
                                                            <option value="12">December</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="dobYear" class="form-label">Birth Year <span class="text-danger">*</span></label>
                                                        <input type="number" class="form-control" id="dobYear" name="dob_year" min="1999" max="2021" required placeholder="e.g., 2015">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <small class="text-muted">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        Please select the student's complete date of birth. The day dropdown will automatically adjust based on the selected month and year.
                                                    </small>
                                                </div>
                                            </div>

                                            <!-- Birth Place -->
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="birthCity" class="form-label">Birth City</label>
                                                        <input type="text" class="form-control" id="birthCity" name="birth_city">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="birthCounty" class="form-label">Birth County</label>
                                                        <select class="form-select" id="birthCounty" name="birth_county">
                                                            <option value="">Select County</option>
                                                            <option value="Bomi County">Bomi County</option>
                                                            <option value="Bong County">Bong County</option>
                                                            <option value="Gbarpolu County">Gbarpolu County</option>
                                                            <option value="Grand Bassa County">Grand Bassa County</option>
                                                            <option value="Grand Cape Mount County">Grand Cape Mount County</option>
                                                            <option value="Grand Gedeh County">Grand Gedeh County</option>
                                                            <option value="Grand Kru County">Grand Kru County</option>
                                                            <option value="Lofa County">Lofa County</option>
                                                            <option value="Margibi County">Margibi County</option>
                                                            <option value="Maryland County">Maryland County</option>
                                                            <option value="Montserrado County">Montserrado County</option>
                                                            <option value="Nimba County">Nimba County</option>
                                                            <option value="River Cess County">River Cess County</option>
                                                            <option value="River Gee County">River Gee County</option>
                                                            <option value="Sinoe County">Sinoe County</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="birthCountry" class="form-label">Birth Country</label>
                                                        <input type="text" class="form-control" id="birthCountry" name="birth_country" value="Liberia">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Current Address -->
                                            <div class="mb-3">
                                                <label for="currentAddress" class="form-label">Current Address <span class="text-danger">*</span></label>
                                                <textarea class="form-control" id="currentAddress" name="current_address" rows="3" required></textarea>
                                            </div>

                                            <!-- Academic Background Section -->
                                            <div class="section-header mt-4">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-school me-2"></i>Academic Background
                                                </h6>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="studentStatus" class="form-label">Student Status</label>
                                                        <select class="form-select" id="studentStatus" name="student_status" required>
                                                            <option value="New Student" selected>New Student</option>
                                                            <option value="Old Student">Old Student</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="gradeApplyingFor" class="form-label">Grade Applying For <span class="text-danger">*</span></label>
                                                        <select class="form-select" id="gradeApplyingFor" name="grade_applying_for" required>
                                                            <option value="">Select Grade</option>
                                                            <option value="Grade One">Grade One</option>
                                                            <option value="Grade Two">Grade Two</option>
                                                            <option value="Grade Three">Grade Three</option>
                                                            <option value="Grade Four">Grade Four</option>
                                                            <option value="Grade Five">Grade Five</option>
                                                            <option value="Grade Six">Grade Six</option>
                                                            <option value="Grade Seven">Grade Seven</option>
                                                            <option value="Grade Eight">Grade Eight</option>
                                                            <option value="Grade Nine">Grade Nine</option>
                                                            <option value="Grade Ten">Grade Ten</option>
                                                            <option value="Grade Eleven">Grade Eleven</option>
                                                            <option value="Grade Twelve">Grade Twelve</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="lastSchoolAttended" class="form-label">Last School Attended</label>
                                                        <input type="text" class="form-control" id="lastSchoolAttended" name="last_school_attended">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="lastSchoolDateAttended" class="form-label">Date Last Attended</label>
                                                        <input type="date" class="form-control" id="lastSchoolDateAttended" name="last_school_date_attended">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="lastSchoolAddress" class="form-label">Last School Address</label>
                                                <textarea class="form-control" id="lastSchoolAddress" name="last_school_address" rows="2"></textarea>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="principalName" class="form-label">Principal Name</label>
                                                        <input type="text" class="form-control" id="principalName" name="principal_name">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="principalContact" class="form-label">Principal Contact</label>
                                                        <input type="text" class="form-control" id="principalContact" name="principal_contact">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Religious & Family Info Section -->
                                            <div class="section-header mt-4">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-heart me-2"></i>Religious & Family Information
                                                </h6>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="churchAffiliation" class="form-label">Church Affiliation</label>
                                                        <input type="text" class="form-control" id="churchAffiliation" name="church_affiliation">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="religion" class="form-label">Religion</label>
                                                        <select class="form-select" id="religion" name="religion" onchange="toggleOtherReligion()">
                                                            <option value="Christianity" selected>Christianity</option>
                                                            <option value="Islam">Islam</option>
                                                            <option value="Traditional African Religion">Traditional African Religion</option>
                                                            <option value="Baha'i">Baha'i</option>
                                                            <option value="Other">Other</option>
                                                        </select>
                                                        <!-- Other Religion Text Field (Hidden by default) -->
                                                        <input type="text" class="form-control mt-2" id="otherReligion" name="other_religion" placeholder="Please specify..." style="display: none;">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Father Information -->
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="fatherName" class="form-label">Father's Name</label>
                                                        <input type="text" class="form-control" id="fatherName" name="father_name">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label for="fatherContact" class="form-label">Father's Contact</label>
                                                        <input type="text" class="form-control" id="fatherContact" name="father_contact">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="mb-3">
                                                        <label class="form-label">Father Alive?</label>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="father_alive" id="fatherAliveYes" value="Yes">
                                                            <label class="form-check-label" for="fatherAliveYes">Yes</label>
                                                        </div>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="father_alive" id="fatherAliveNo" value="No">
                                                            <label class="form-check-label" for="fatherAliveNo">No</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Mother Information -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="motherName" class="form-label">Mother's Name</label>
                                                        <input type="text" class="form-control" id="motherName" name="mother_name">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="motherContact" class="form-label">Mother's Contact</label>
                                                        <input type="text" class="form-control" id="motherContact" name="mother_contact">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Guardian Information -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="guardianName" class="form-label">Guardian's Name <small class="text-muted">(if parents not available)</small></label>
                                                        <input type="text" class="form-control" id="guardianName" name="guardian_name">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="guardianContact" class="form-label">Guardian's Contact</label>
                                                        <input type="text" class="form-control" id="guardianContact" name="guardian_contact">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Emergency Contact -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="emergencyContactName" class="form-label">Emergency Contact Name <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" id="emergencyContactName" name="emergency_contact_name" required>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="emergencyContactPhone" class="form-label">Emergency Contact Phone <span class="text-danger">*</span></label>
                                                        <input type="tel" class="form-control" id="emergencyContactPhone" name="emergency_contact_phone" required>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Document Uploads Section -->
                                            <div class="section-header mt-4">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-file-upload me-2"></i>Required Documents
                                                </h6>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="transcriptCheck" name="transcript">
                                                        <label class="form-check-label" for="transcriptCheck">
                                                            <i class="fas fa-file-alt me-2"></i>Transcript Available
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="reportCardCheck" name="report_card">
                                                        <label class="form-check-label" for="reportCardCheck">
                                                            <i class="fas fa-file-alt me-2"></i>Report Card Available
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="healthCertificateCheck" name="health_certificate">
                                                        <label class="form-check-label" for="healthCertificateCheck">
                                                            <i class="fas fa-file-medical me-2"></i>Health Certificate Available
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-3">
                                                        <input class="form-check-input" type="checkbox" id="passportPhotosCheck" name="passport_photos">
                                                        <label class="form-check-label" for="passportPhotosCheck">
                                                            <i class="fas fa-camera me-2"></i>Passport Photos (2) Available
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Photo Upload Section (moved inside form) -->
                                            <div class="section-header mt-4">
                                                <h6 class="text-primary border-bottom pb-2 mb-3">
                                                    <i class="fas fa-camera me-2"></i>Student Photo
                                                </h6>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="studentPhoto" class="form-label">Upload Student Photo</label>
                                                        <input type="file" class="form-control" id="studentPhoto" name="passport_photo" accept="image/*" onchange="previewPhoto(this)">
                                                        <small class="text-muted">Accepted formats: JPG, PNG, GIF (Max 5MB)</small>
                                                    </div>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" id="removePhotoBtn" onclick="removePhoto()" style="display: none;">
                                                        <i class="fas fa-trash me-1"></i>Remove Photo
                                                    </button>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="photo-upload-container">
                                                        <div class="photo-preview mb-3" id="photoPreview">
                                                            <img id="studentPhotoPreview" src="" alt="Student Photo" class="img-fluid rounded" style="display: none; max-width: 200px; max-height: 250px;">
                                                            <div id="photoPlaceholder" class="photo-placeholder">
                                                                <i class="fas fa-user fa-3x text-muted mb-2"></i>
                                                                <p class="text-muted mb-0">Student Photo Preview</p>
                                                                <small class="text-muted">Upload a passport-style photo</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Submit Button -->
                                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                                <button type="button" class="btn btn-secondary me-md-2" onclick="clearApplicationForm()">
                                                    <i class="fas fa-undo me-2"></i>Clear Form
                                                </button>
                                                <button type="button" class="btn btn-primary" onclick="submitApplicationForm()">
                                                    <i class="fas fa-paper-plane me-2"></i>Submit Application
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <!-- Application Status Section -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Application Status</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="applicationStatus">
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-clock me-2"></i>Ready to submit application
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Administrative Section (Admin Only) -->
                            <div class="col-md-6">
                                <div class="card admin-only" style="display: none;">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0"><i class="fas fa-user-shield me-2"></i>Administrative Fields</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="gradePlacement" class="form-label">Grade Placement</label>
                                            <input type="text" class="form-control form-control-sm" id="gradePlacement" name="grade_placement">
                                        </div>
                                        <div class="mb-3">
                                            <label for="admissionStatus" class="form-label">Admission Status</label>
                                            <select class="form-select form-select-sm" id="admissionStatus" name="admission_status">
                                                <option value="Regular">Regular</option>
                                                <option value="Probation">Probation</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Documents Verified</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="transcriptVerified" name="transcript_verified">
                                                <label class="form-check-label" for="transcriptVerified">Transcript</label>
                                            </div>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="reportCardVerified" name="report_card_verified">
                                                <label class="form-check-label" for="reportCardVerified">Report Card</label>
                                            </div>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="healthCertVerified" name="health_cert_verified">
                                                <label class="form-check-label" for="healthCertVerified">Health Certificate</label>
                                            </div>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="passportPhotosVerified" name="passport_photos_verified">
                                                <label class="form-check-label" for="passportPhotosVerified">Passport Photos</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Application Management Section -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Application Management</h5>
                                        <div>
                                            <button class="btn btn-sm btn-light" onclick="refreshApplicationsList()">
                                                <i class="fas fa-sync-alt me-1"></i>Refresh
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="exportApplications()">
                                                <i class="fas fa-file-excel me-1"></i>Export
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!-- Search and Filter -->
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <input type="text" class="form-control" id="applicationSearch" placeholder="Search by name...">
                                            </div>
                                            <div class="col-md-3">
                                                <select class="form-select" id="applicationGradeFilter">
                                                    <option value="">All Grades</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <select class="form-select" id="applicationStatusFilter">
                                                    <option value="">All Status</option>
                                                    <option value="Pending">Pending</option>
                                                    <option value="Approved">Approved</option>
                                                    <option value="Rejected">Rejected</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-outline-primary w-100" onclick="filterApplications()">
                                                    <i class="fas fa-filter me-1"></i>Filter
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Applications Table -->
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th>Photo</th>
                                                        <th>Full Name</th>
                                                        <th>Grade Applied</th>
                                                        <th>Date of Birth</th>
                                                        <th>Emergency Contact</th>
                                                        <th>Status</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="applicationsTable">
                                                    <tr>
                                                        <td colspan="7" class="text-center text-muted py-4">
                                                            <i class="fas fa-inbox fa-2x mb-2"></i>
                                                            <p>No applications submitted yet</p>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fees Management Tab -->
                    <div class="tab-pane fade" id="feesManagement">
                        <div class="container-fluid">
                            <h2 class="mb-4"><i class="fas fa-money-bill-wave me-2"></i>Fees Management & Payment Tracking</h2>

                            <!-- Fee Structure Management -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Fee Structure Setup</h5>
                                        </div>
                                        <div class="card-body">
                                            <form id="feeStructureForm">
                                                <div class="mb-3">
                                                    <label for="feeClass" class="form-label">Class</label>
                                                    <select class="form-select" id="feeClass" required>
                                                        <option value="">Select Class</option>
                                                        <option value="Grade One">Grade One</option>
                                                        <option value="Grade Two">Grade Two</option>
                                                        <option value="Grade Three">Grade Three</option>
                                                        <option value="Grade Four">Grade Four</option>
                                                        <option value="Grade Five">Grade Five</option>
                                                        <option value="Grade Six">Grade Six</option>
                                                        <option value="Grade Seven">Grade Seven</option>
                                                        <option value="Grade Eight">Grade Eight</option>
                                                        <option value="Grade Nine">Grade Nine</option>
                                                        <option value="Grade Ten">Grade Ten</option>
                                                        <option value="Grade Eleven">Grade Eleven</option>
                                                        <option value="Grade Twelve">Grade Twelve</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="feeName" class="form-label">Fee Category</label>
                                                    <select class="form-select" id="feeName" onchange="toggleCustomFeeCategory()" required>
                                                        <option value="">Select Category</option>
                                                        <option value="Tuition">Tuition</option>
                                                        <option value="Registration">Registration</option>
                                                        <option value="Books">Books</option>
                                                        <option value="Uniform">Uniform</option>
                                                        <option value="Transport">Transport</option>
                                                        <option value="Meals">Meals</option>
                                                        <option value="Activities">Activities</option>
                                                        <option value="Other">Other</option>
                                                        <option value="Custom">+ Add Custom Category</option>
                                                    </select>
                                                    <!-- Custom Fee Category Input (Hidden by default) -->
                                                    <input type="text" class="form-control mt-2" id="customFeeName" placeholder="Enter custom fee category..." style="display: none;">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="feeAmount" class="form-label">Amount (USD)</label>
                                                    <input type="number" class="form-control" id="feeAmount" min="0" step="0.01" required>
                                                </div>
                                                <button type="button" class="btn btn-primary w-100" onclick="addFeeStructure()">
                                                    <i class="fas fa-plus me-2"></i>Add Fee
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Current Fee Structure</h5>
                                            <div>
                                                <button class="btn btn-sm btn-light me-2" onclick="updateFeeStructureTable()" title="Refresh table display">
                                                    <i class="fas fa-sync me-1"></i>Refresh
                                                </button>
                                                <button class="btn btn-sm btn-danger me-2" onclick="deleteEntireFeeStructure()" title="Delete all fee structures">
                                                    <i class="fas fa-trash-alt me-1"></i>Delete All
                                                </button>
                                                <button class="btn btn-sm btn-secondary" onclick="resetFeeStructure()" title="Clear all fee structures">
                                                    <i class="fas fa-broom me-1"></i>Clear All
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body p-0">
                                            <div class="table-responsive">
                                                <table class="table table-hover mb-0 fee-structure-table">
                                                    <thead style="background: #343a40 !important;">
                                                        <tr class="header-row" style="background: #343a40 !important; color: white !important;">
                                                            <th class="header-class" style="background: #343a40 !important; color: white !important; padding: 15px 20px; font-weight: 700; font-size: 14px; text-align: center; border: 1px solid #495057;">
                                                                <i class="fas fa-graduation-cap me-2"></i>
                                                                <span>Class</span>
                                                            </th>
                                                            <th class="header-category" style="background: #343a40 !important; color: white !important; padding: 15px 20px; font-weight: 700; font-size: 14px; text-align: center; border: 1px solid #495057;">
                                                                <i class="fas fa-tag me-2"></i>
                                                                <span>Fee Category</span>
                                                            </th>
                                                            <th class="header-amount" style="background: #343a40 !important; color: white !important; padding: 15px 20px; font-weight: 700; font-size: 14px; text-align: center; border: 1px solid #495057;">
                                                                <i class="fas fa-dollar-sign me-2"></i>
                                                                <span>Amount (USD)</span>
                                                            </th>
                                                            <th class="header-actions" style="background: #343a40 !important; color: white !important; padding: 15px 20px; font-weight: 700; font-size: 14px; text-align: center; border: 1px solid #495057;">
                                                                <i class="fas fa-cogs me-2"></i>
                                                                <span>Actions</span>
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="feeStructuresTable">
                                                        <!-- Fee structures will be dynamically added here -->
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="card-footer bg-light text-muted text-center py-2">
                                                <small><i class="fas fa-info-circle me-1"></i>Fee structure defines the required payments for each grade level</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Student Payment Management -->
                            <div class="row">
                                <!-- Student Selection -->
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Select Student</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="paymentStudentClass" class="form-label">Class</label>
                                                <select class="form-select" id="paymentStudentClass" onchange="updatePaymentStudentDropdown()">
                                                    <option value="">Select Class</option>
                                                    <option value="Grade One">Grade One</option>
                                                    <option value="Grade Two">Grade Two</option>
                                                    <option value="Grade Three">Grade Three</option>
                                                    <option value="Grade Four">Grade Four</option>
                                                    <option value="Grade Five">Grade Five</option>
                                                    <option value="Grade Six">Grade Six</option>
                                                    <option value="Grade Seven">Grade Seven</option>
                                                    <option value="Grade Eight">Grade Eight</option>
                                                    <option value="Grade Nine">Grade Nine</option>
                                                    <option value="Grade Ten">Grade Ten</option>
                                                    <option value="Grade Eleven">Grade Eleven</option>
                                                    <option value="Grade Twelve">Grade Twelve</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="paymentStudent" class="form-label">Student</label>
                                                <select class="form-select" id="paymentStudent" onchange="loadStudentPaymentInfo()">
                                                    <option value="">Select Student</option>
                                                </select>
                                            </div>
                                            <button class="btn btn-info w-100" onclick="loadStudentPaymentInfo()">
                                                <i class="fas fa-search me-2"></i>Load Payment Info
                                            </button>
                                            <div class="mt-3">
                                                <button class="btn btn-sm btn-outline-secondary w-100" onclick="debugStudentPaymentData()">
                                                    <i class="fas fa-bug me-1"></i>Debug Student Data
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Fee Summary -->
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Fee Summary & Payment Status</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="paymentSummary">
                                                <div class="text-center text-muted py-4">
                                                    <i class="fas fa-user-plus fa-3x mb-3"></i>
                                                    <p>Select a student to view payment information</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Form and History -->
                            <div class="row mt-4" id="paymentFormSection">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Record Student Payment</h5>
                                        </div>
                                        <div class="card-body">
                                            <!-- Example Usage -->
                                            <div class="alert alert-info mb-3">
                                                <i class="fas fa-info-circle me-2"></i>
                                                <strong>Example:</strong> John paying $50 for Tuition in Grade 6
                                                <br><small>1. Select student above → 2. Choose fee category → 3. Enter amount → 4. Record payment</small>
                                            </div>

                                            <form id="paymentForm">
                                                <div class="mb-3">
                                                    <label for="paymentCategory" class="form-label">Fee Category <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="paymentCategory" onchange="toggleCustomPaymentCategory()" required>
                                                        <option value="">Select Category</option>
                                                        <option value="Tuition">Tuition</option>
                                                        <option value="Registration">Registration</option>
                                                        <option value="Books">Books</option>
                                                        <option value="Uniform">Uniform</option>
                                                        <option value="Transport">Transport</option>
                                                        <option value="Meals">Meals</option>
                                                        <option value="Activities">Activities</option>
                                                        <option value="Other">Other</option>
                                                        <option value="Custom">+ Add Custom Category</option>
                                                    </select>
                                                    <!-- Custom Payment Category Input (Hidden by default) -->
                                                    <input type="text" class="form-control mt-2" id="customPaymentCategory" placeholder="Enter custom fee category..." style="display: none;">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="paymentAmount" class="form-label">Payment Amount (USD) <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="paymentAmount" step="0.01" min="0" placeholder="e.g., 50.00" required>
                                                    <div class="form-text">Enter the amount being paid for this fee category</div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="paymentMethod" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="paymentMethod" required>
                                                        <option value="">Select Method</option>
                                                        <option value="Cash">Cash</option>
                                                        <option value="Bank Transfer">Bank Transfer</option>
                                                        <option value="Mobile Money">Mobile Money</option>
                                                        <option value="Check">Check</option>
                                                        <option value="Card">Card</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="paymentReference" class="form-label">Reference/Receipt No.</label>
                                                    <input type="text" class="form-control" id="paymentReference" placeholder="Transaction ID, Receipt #, etc. (Optional)">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="paymentNotes" class="form-label">Notes</label>
                                                    <textarea class="form-control" id="paymentNotes" rows="2" placeholder="Additional notes about this payment (Optional)"></textarea>
                                                </div>

                                                <!-- Payment Summary -->
                                                <div class="alert alert-light border mb-3" id="paymentPreview" style="display: none;">
                                                    <h6 class="mb-2"><i class="fas fa-receipt me-2"></i>Payment Summary</h6>
                                                    <div id="paymentPreviewContent"></div>
                                                </div>

                                                <button type="button" class="btn btn-success w-100 btn-lg" onclick="recordPayment()">
                                                    <i class="fas fa-save me-2"></i>Record Payment
                                                </button>
                                                <div class="text-center mt-2">
                                                    <small class="text-muted">A receipt will be automatically generated</small>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment History -->
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-secondary text-white">
                                            <h5 class="mb-0"><i class="fas fa-history me-2"></i>Payment History</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="paymentHistory">
                                                <p class="text-muted">No payments recorded yet</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- All Payment Records -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>All Payment Records</h5>
                                            <div>
                                                <button class="btn btn-sm btn-light" onclick="exportPaymentRecords()" title="Export to CSV">
                                                    <i class="fas fa-file-excel me-1"></i>Export
                                                </button>
                                                <button class="btn btn-sm btn-warning" onclick="deletePaymentsByCriteria()" title="Clean up specific records">
                                                    <i class="fas fa-broom me-1"></i>Clean Up
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="clearAllPaymentRecords()" title="Delete all payment records">
                                                    <i class="fas fa-trash-alt me-1"></i>Clear All
                                                </button>
                                                <span class="badge bg-light text-dark ms-2" id="paymentRecordsCountBadge">0</span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-4">
                                                    <input type="text" class="form-control" id="paymentSearchInput" placeholder="Search by student name...">
                                                </div>
                                                <div class="col-md-3">
                                                    <select class="form-select" id="paymentClassFilter">
                                                        <option value="">All Classes</option>
                                                        <option value="Grade One">Grade One</option>
                                                        <option value="Grade Two">Grade Two</option>
                                                        <option value="Grade Three">Grade Three</option>
                                                        <option value="Grade Four">Grade Four</option>
                                                        <option value="Grade Five">Grade Five</option>
                                                        <option value="Grade Six">Grade Six</option>
                                                        <option value="Grade Seven">Grade Seven</option>
                                                        <option value="Grade Eight">Grade Eight</option>
                                                        <option value="Grade Nine">Grade Nine</option>
                                                        <option value="Grade Ten">Grade Ten</option>
                                                        <option value="Grade Eleven">Grade Eleven</option>
                                                        <option value="Grade Twelve">Grade Twelve</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <select class="form-select" id="paymentCategoryFilter">
                                                        <option value="">All Categories</option>
                                                        <option value="Tuition">Tuition</option>
                                                        <option value="Registration">Registration</option>
                                                        <option value="Books">Books</option>
                                                        <option value="Uniform">Uniform</option>
                                                        <option value="Transport">Transport</option>
                                                        <option value="Meals">Meals</option>
                                                        <option value="Activities">Activities</option>
                                                        <option value="Other">Other</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <button class="btn btn-outline-primary w-100" onclick="filterPaymentRecords()">
                                                        <i class="fas fa-filter me-1"></i>Filter
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>Date</th>
                                                            <th>Student</th>
                                                            <th>Class</th>
                                                            <th>Category</th>
                                                            <th>Amount</th>
                                                            <th>Method</th>
                                                            <th>Reference</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="paymentRecordsTable">
                                                        <!-- Payment records will be dynamically added here -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Outstanding Fees Section -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Outstanding Fees</h5>
                                            <div>
                                                <button class="btn btn-sm btn-dark" onclick="generateOutstandingFeesReport()">
                                                    <i class="fas fa-file-pdf me-1"></i>Generate Report
                                                </button>
                                                <button class="btn btn-sm btn-success" onclick="exportOutstandingFees()">
                                                    <i class="fas fa-file-excel me-1"></i>Export
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <input type="text" class="form-control" id="feeSearchInput" placeholder="Search by student name...">
                                                </div>
                                                <div class="col-md-4">
                                                    <select class="form-select" id="feeFilterClass">
                                                        <option value="">All Classes</option>
                                                        <option value="Grade One">Grade One</option>
                                                        <option value="Grade Two">Grade Two</option>
                                                        <option value="Grade Three">Grade Three</option>
                                                        <option value="Grade Four">Grade Four</option>
                                                        <option value="Grade Five">Grade Five</option>
                                                        <option value="Grade Six">Grade Six</option>
                                                        <option value="Grade Seven">Grade Seven</option>
                                                        <option value="Grade Eight">Grade Eight</option>
                                                        <option value="Grade Nine">Grade Nine</option>
                                                        <option value="Grade Ten">Grade Ten</option>
                                                        <option value="Grade Eleven">Grade Eleven</option>
                                                        <option value="Grade Twelve">Grade Twelve</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <button class="btn btn-outline-primary w-100" onclick="filterOutstandingFees()">
                                                        <i class="fas fa-filter me-1"></i>Filter
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead class="table-warning">
                                                        <tr>
                                                            <th>Student Name</th>
                                                            <th>Class</th>
                                                            <th>Total Fees</th>
                                                            <th>Amount Paid</th>
                                                            <th>Balance</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="outstandingFeesTable">
                                                        <!-- Outstanding fees will be dynamically added here -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Parent Management Tab -->
                    <div class="tab-pane fade" id="parentManagement">
                        <h2>Parent Management</h2>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Add Parent</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="addParentForm">
                                            <div class="mb-3">
                                                <label for="parentName" class="form-label">Full Name</label>
                                                <input type="text" class="form-control" id="parentName" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="parentPhone" class="form-label">Phone Number</label>
                                                <input type="tel" class="form-control" id="parentPhone">
                                            </div>
                                            <div class="mb-3">
                                                <label for="parentEmail" class="form-label">Email (Optional)</label>
                                                <input type="email" class="form-control" id="parentEmail">
                                            </div>
                                            <div class="mb-3">
                                                <label for="parentAddress" class="form-label">Address</label>
                                                <textarea class="form-control" id="parentAddress" rows="2"></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-primary">Add Parent</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Link Parent to Student</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="linkParentForm">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="linkParentSelect" class="form-label">Parent</label>
                                                        <select class="form-select" id="linkParentSelect">
                                                            <option value="">Select Parent</option>
                                                            <!-- Parents will be dynamically added here -->
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="linkStudentClass" class="form-label">Class</label>
                                                        <select class="form-select" id="linkStudentClass">
                                                            <option value="">Select Class</option>
                                                            <option value="Grade One">Grade One</option>
                                                            <option value="Grade Two">Grade Two</option>
                                                            <option value="Grade Three">Grade Three</option>
                                                            <option value="Grade Four">Grade Four</option>
                                                            <option value="Grade Five">Grade Five</option>
                                                            <option value="Grade Six">Grade Six</option>
                                                            <option value="Grade Seven">Grade Seven</option>
                                                            <option value="Grade Eight">Grade Eight</option>
                                                            <option value="Grade Nine">Grade Nine</option>
                                                            <option value="Grade Ten">Grade Ten</option>
                                                            <option value="Grade Eleven">Grade Eleven</option>
                                                            <option value="Grade Twelve">Grade Twelve</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="linkStudentSelect" class="form-label">Student</label>
                                                <select class="form-select" id="linkStudentSelect">
                                                    <option value="">Select Student</option>
                                                    <!-- Students will be dynamically added here -->
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="relationshipType" class="form-label">Relationship</label>
                                                <select class="form-select" id="relationshipType">
                                                    <option value="Father">Father</option>
                                                    <option value="Mother">Mother</option>
                                                    <option value="Guardian">Guardian</option>
                                                    <option value="Other">Other</option>
                                                </select>
                                            </div>
                                            <button type="submit" class="btn btn-primary">Link Parent to Student</button>
                                        </form>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5>All Parents</h5>
                                        <span class="badge bg-primary" id="parentsCountBadge">0</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Parent Name</th>
                                                        <th>Contact Number</th>
                                                        <th>Email</th>
                                                        <th>Students</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="parentsTable">
                                                    <!-- Parents will be dynamically added here -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5>Parent-Student Relationships</h5>
                                        <span class="badge bg-primary" id="relationshipsCountBadge">0</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Parent Name</th>
                                                        <th>Contact Number</th>
                                                        <th>Student Name</th>
                                                        <th>Class</th>
                                                        <th>Relationship</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="parentStudentTable">
                                                    <!-- Parent-student relationships will be dynamically added here -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Teacher Management Tab -->
                    <div class="tab-pane fade" id="teacherManagement">
                        <h2>Teacher Management</h2>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Add Teacher</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="addTeacherForm">
                                            <div class="mb-3">
                                                <label for="teacherName" class="form-label">Full Name</label>
                                                <input type="text" class="form-control" id="teacherName" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="teacherPhone" class="form-label">Phone Number</label>
                                                <input type="tel" class="form-control" id="teacherPhone">
                                            </div>
                                            <div class="mb-3">
                                                <label for="teacherEmail" class="form-label">Email</label>
                                                <input type="email" class="form-control" id="teacherEmail">
                                            </div>
                                            <div class="mb-3">
                                                <label for="teacherQualification" class="form-label">Qualification</label>
                                                <input type="text" class="form-control" id="teacherQualification">
                                            </div>
                                            <div class="mb-3">
                                                <label for="teacherJoinDate" class="form-label">Join Date</label>
                                                <input type="date" class="form-control" id="teacherJoinDate">
                                            </div>
                                            <button type="submit" class="btn btn-primary">Add Teacher</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Assign Teacher to Class</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="assignTeacherForm">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="assignTeacherSelect" class="form-label">Teacher</label>
                                                        <select class="form-select" id="assignTeacherSelect">
                                                            <option value="">Select Teacher</option>
                                                            <!-- Teachers will be dynamically added here -->
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="assignClass" class="form-label">Class</label>
                                                        <select class="form-select" id="assignClass">
                                                            <option value="">Select Class</option>
                                                            <option value="Grade One">Grade One</option>
                                                            <option value="Grade Two">Grade Two</option>
                                                            <option value="Grade Three">Grade Three</option>
                                                            <option value="Grade Four">Grade Four</option>
                                                            <option value="Grade Five">Grade Five</option>
                                                            <option value="Grade Six">Grade Six</option>
                                                            <option value="Grade Seven">Grade Seven</option>
                                                            <option value="Grade Eight">Grade Eight</option>
                                                            <option value="Grade Nine">Grade Nine</option>
                                                            <option value="Grade Ten">Grade Ten</option>
                                                            <option value="Grade Eleven">Grade Eleven</option>
                                                            <option value="Grade Twelve">Grade Twelve</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="assignSubject" class="form-label">Subject</label>
                                                <select class="form-select" id="assignSubject">
                                                    <option value="">Select Subject</option>
                                                    <!-- Subjects will be dynamically added here based on selected class -->
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="isClassTeacher">
                                                    <label class="form-check-label" for="isClassTeacher">
                                                        Assign as Class Teacher
                                                    </label>
                                                </div>
                                            </div>
                                            <button type="submit" class="btn btn-primary">Assign Teacher</button>
                                        </form>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5>Teacher Assignments</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Teacher Name</th>
                                                        <th>Class</th>
                                                        <th>Subject</th>
                                                        <th>Class Teacher</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="teacherAssignmentsTable">
                                                    <!-- Teacher assignments will be dynamically added here -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Class Schedule Section -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5>Class Schedules</h5>
                                        <div>
                                            <button class="btn btn-sm btn-primary" id="addScheduleBtn">
                                                <i class="fas fa-plus"></i> Add Schedule
                                            </button>
                                            <button class="btn btn-sm btn-success" id="printScheduleBtn">
                                                <i class="fas fa-print"></i> Print Schedule
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <label for="scheduleTeacherSelect" class="form-label">Select Teacher</label>
                                                <select class="form-select" id="scheduleTeacherSelect">
                                                    <option value="">All Teachers</option>
                                                    <!-- Teachers will be dynamically added here -->
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="scheduleDaySelect" class="form-label">Day</label>
                                                <select class="form-select" id="scheduleDaySelect">
                                                    <option value="">All Days</option>
                                                    <option value="Monday">Monday</option>
                                                    <option value="Tuesday">Tuesday</option>
                                                    <option value="Wednesday">Wednesday</option>
                                                    <option value="Thursday">Thursday</option>
                                                    <option value="Friday">Friday</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="scheduleClassSelect" class="form-label">Class</label>
                                                <select class="form-select" id="scheduleClassSelect">
                                                    <option value="">All Classes</option>
                                                    <!-- Classes will be dynamically added here -->
                                                </select>
                                            </div>
                                        </div>

                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Day</th>
                                                        <th>Period</th>
                                                        <th>Time</th>
                                                        <th>Teacher</th>
                                                        <th>Class</th>
                                                        <th>Subject</th>
                                                        <th>Room</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="scheduleTable">
                                                    <!-- Schedule entries will be dynamically added here -->
                                                    <tr>
                                                        <td colspan="8" class="text-center">No schedule entries found.</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Tab -->
                    <div class="tab-pane fade" id="attendance">
                        <h2>Attendance Management</h2>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Take Attendance</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="attendanceForm">
                                            <div class="mb-3">
                                                <label for="attendanceClass" class="form-label">Class</label>
                                                <select class="form-select" id="attendanceClass">
                                                    <option value="">Select Class</option>
                                                    <!-- Classes will be dynamically added here -->
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="attendanceDate" class="form-label">Date</label>
                                                <input type="date" class="form-control" id="attendanceDate" value="">
                                            </div>
                                            <button type="submit" class="btn btn-primary">Load Students</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Student Attendance</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="attendanceList">
                                            <p class="text-center text-muted">Select a class and date to take attendance</p>
                                        </div>
                                        <button id="saveAttendanceBtn" class="btn btn-success mt-3 d-none">
                                            <i class="fas fa-save me-2"></i> Save Attendance
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Attendance Reports</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <form id="attendanceReportForm">
                                                    <div class="mb-3">
                                                        <label for="reportClass" class="form-label">Class</label>
                                                        <select class="form-select" id="reportClass">
                                                            <option value="">Select Class</option>
                                                            <!-- Classes will be dynamically added here -->
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="reportDateFrom" class="form-label">From Date</label>
                                                        <input type="date" class="form-control" id="reportDateFrom">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="reportDateTo" class="form-label">To Date</label>
                                                        <input type="date" class="form-control" id="reportDateTo">
                                                    </div>
                                                    <button type="submit" class="btn btn-primary">Generate Report</button>
                                                </form>
                                            </div>
                                            <div class="col-md-8">
                                                <div id="attendanceReportResult">
                                                    <p class="text-center text-muted">Select parameters to generate an attendance report</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Behavior & Discipline Tab -->
                    <div class="tab-pane fade" id="behavior">
                        <div class="container-fluid">
                            <h2 class="mb-4"><i class="fas fa-clipboard-list me-2"></i>Behavior & Discipline Tracking</h2>

                            <!-- Dashboard Overview -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="totalIncidentsCount">0</h4>
                                                    <p class="mb-0">Total Incidents</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="openIncidentsCount">0</h4>
                                                    <p class="mb-0">Open Incidents</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-folder-open fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="activeActionsCount">0</h4>
                                                    <p class="mb-0">Active Actions</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-gavel fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="studentsWithIncidentsCount">0</h4>
                                                    <p class="mb-0">Students Involved</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-users fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Log Behavior Incident -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-plus-circle me-2"></i>Log Behavior Incident</h5>
                                        </div>
                                        <div class="card-body">
                                            <form id="behaviorIncidentForm">
                                                <div class="mb-3">
                                                    <label for="behaviorStudentSelect" class="form-label">Student *</label>
                                                    <select class="form-select" id="behaviorStudentSelect" required>
                                                        <option value="">Select Student</option>
                                                    </select>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="incidentType" class="form-label">Incident Type *</label>
                                                            <div class="input-group">
                                                                <select class="form-select" id="incidentType" required onchange="handleIncidentTypeChange()">
                                                                    <option value="">Select Type</option>
                                                                    <option value="Disruptive Behavior">Disruptive Behavior</option>
                                                                    <option value="Disrespect">Disrespect</option>
                                                                    <option value="Fighting">Fighting</option>
                                                                    <option value="Bullying">Bullying</option>
                                                                    <option value="Tardiness">Tardiness</option>
                                                                    <option value="Uniform Violation">Uniform Violation</option>
                                                                    <option value="Academic Dishonesty">Academic Dishonesty</option>
                                                                    <option value="Property Damage">Property Damage</option>
                                                                    <option value="Inappropriate Language">Inappropriate Language</option>
                                                                    <option value="Add Custom Type">+ Add Custom Type</option>
                                                                </select>
                                                                <button class="btn btn-outline-secondary" type="button" onclick="showManageIncidentTypesModal()" title="Manage Incident Types">
                                                                    <i class="fas fa-cog"></i>
                                                                </button>
                                                            </div>
                                                            <input type="text" class="form-control mt-2" id="customIncidentType"
                                                                   placeholder="Enter custom incident type..." style="display: none;">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="incidentSeverity" class="form-label">Severity *</label>
                                                            <select class="form-select" id="incidentSeverity" required>
                                                                <option value="">Select Severity</option>
                                                                <option value="Minor">Minor</option>
                                                                <option value="Moderate">Moderate</option>
                                                                <option value="Major">Major</option>
                                                                <option value="Severe">Severe</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="incidentDescription" class="form-label">Description *</label>
                                                    <textarea class="form-control" id="incidentDescription" rows="3" required
                                                              placeholder="Describe the incident in detail..."></textarea>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="mb-3">
                                                            <label for="incidentDate" class="form-label">Incident Date *</label>
                                                            <input type="date" class="form-control" id="incidentDate" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="mb-3">
                                                            <label for="incidentLocation" class="form-label">Location</label>
                                                            <input type="text" class="form-control" id="incidentLocation"
                                                                   placeholder="e.g., Classroom, Playground, Cafeteria">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="mb-3">
                                                            <label for="reportedBy" class="form-label">Reported By</label>
                                                            <input type="text" class="form-control" id="reportedBy"
                                                                   placeholder="Teacher/Staff name">
                                                        </div>
                                                    </div>
                                                </div>
                                                <button type="button" class="btn btn-primary" onclick="logBehaviorIncident()">
                                                    <i class="fas fa-save me-2"></i>Log Incident
                                                </button>
                                                <button type="button" class="btn btn-secondary ms-2" onclick="clearBehaviorForm()">
                                                    <i class="fas fa-times me-2"></i>Clear
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-clock me-2"></i>Recent Incidents</h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="recentIncidentsList">
                                                <p class="text-muted">No recent incidents</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Behavior Records Table -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5><i class="fas fa-list me-2"></i>Behavior Records</h5>
                                            <div>
                                                <button class="btn btn-outline-primary btn-sm" onclick="updateBehaviorRecordsTable()">
                                                    <i class="fas fa-sync-alt me-1"></i>Refresh
                                                </button>
                                                <button class="btn btn-outline-success btn-sm" onclick="exportBehaviorRecords()">
                                                    <i class="fas fa-download me-1"></i>Export
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Student</th>
                                                            <th>Type & Severity</th>
                                                            <th>Description</th>
                                                            <th>Date & Reporter</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="behaviorRecordsTable">
                                                        <tr>
                                                            <td colspan="6" class="text-center text-muted">
                                                                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                                                                <p>No behavior records found.</p>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Disciplinary Actions Section -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5><i class="fas fa-gavel me-2"></i>Create Disciplinary Action</h5>
                                        </div>
                                        <div class="card-body">
                                            <form id="disciplinaryActionForm">
                                                <div class="mb-3">
                                                    <label for="disciplinaryStudentSelect" class="form-label">Student *</label>
                                                    <select class="form-select" id="disciplinaryStudentSelect" required>
                                                        <option value="">Select Student</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="actionType" class="form-label">Action Type *</label>
                                                    <select class="form-select" id="actionType" required>
                                                        <option value="">Select Action</option>
                                                        <option value="Verbal Warning">Verbal Warning</option>
                                                        <option value="Written Warning">Written Warning</option>
                                                        <option value="Detention">Detention</option>
                                                        <option value="Suspension">Suspension</option>
                                                        <option value="Parent Meeting">Parent Meeting</option>
                                                        <option value="Community Service">Community Service</option>
                                                        <option value="Counseling">Counseling</option>
                                                        <option value="Behavior Contract">Behavior Contract</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="actionDescription" class="form-label">Description *</label>
                                                    <textarea class="form-control" id="actionDescription" rows="3" required
                                                              placeholder="Describe the disciplinary action..."></textarea>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="actionStartDate" class="form-label">Start Date *</label>
                                                            <input type="date" class="form-control" id="actionStartDate" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="actionEndDate" class="form-label">End Date</label>
                                                            <input type="date" class="form-control" id="actionEndDate">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="actionAssignedBy" class="form-label">Assigned By</label>
                                                    <input type="text" class="form-control" id="actionAssignedBy"
                                                           placeholder="Teacher/Administrator name">
                                                </div>
                                                <button type="button" class="btn btn-warning" onclick="createDisciplinaryAction()">
                                                    <i class="fas fa-gavel me-2"></i>Create Action
                                                </button>
                                                <button type="button" class="btn btn-secondary ms-2" onclick="clearDisciplinaryForm()">
                                                    <i class="fas fa-times me-2"></i>Clear
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Disciplinary Actions Table -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5><i class="fas fa-gavel me-2"></i>Disciplinary Actions & Conduct Reports</h5>
                                                <div>
                                                    <button class="btn btn-outline-primary btn-sm" onclick="updateDisciplinaryActionsTable()">
                                                        <i class="fas fa-sync-alt me-1"></i>Refresh
                                                    </button>
                                                    <button class="btn btn-outline-success btn-sm" onclick="exportDisciplinaryActions()">
                                                        <i class="fas fa-download me-1"></i>Export
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Quick Conduct Report Generator -->
                                            <div class="row g-2 align-items-end">
                                                <div class="col-md-4">
                                                    <label for="quickConductStudent" class="form-label small">Generate Conduct Report</label>
                                                    <select class="form-select form-select-sm" id="quickConductStudent">
                                                        <option value="">Select Student</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="quickConductStartDate" class="form-label small">Start Date</label>
                                                    <input type="date" class="form-control form-control-sm" id="quickConductStartDate">
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="quickConductEndDate" class="form-label small">End Date</label>
                                                    <input type="date" class="form-control form-control-sm" id="quickConductEndDate">
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-info btn-sm w-100" onclick="generateQuickConductReport()">
                                                        <i class="fas fa-chart-bar me-1"></i>Generate
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Student</th>
                                                            <th>Action Type</th>
                                                            <th>Description</th>
                                                            <th>Date Range</th>
                                                            <th>Status</th>
                                                            <th>Assigned By</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="disciplinaryActionsTable">
                                                        <tr>
                                                            <td colspan="7" class="text-center text-muted">
                                                                <i class="fas fa-gavel fa-2x mb-2"></i>
                                                                <p>No disciplinary actions found.</p>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Staff Management & HR Tab -->
                    <div class="tab-pane fade" id="staffManagement">
                        <div class="container-fluid">
                            <h2 class="mb-4"><i class="fas fa-users-cog me-2"></i>Staff Management & HR</h2>

                            <!-- HR Dashboard Overview -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="totalEmployeesCount">0</h4>
                                                    <p class="mb-0">Total Employees</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-users fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="activeEmployeesCount">0</h4>
                                                    <p class="mb-0">Active Staff</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-user-check fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="pendingEvaluationsCount">0</h4>
                                                    <p class="mb-0">Pending Evaluations</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-clipboard-check fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="substituteRequestsCount">0</h4>
                                                    <p class="mb-0">Substitute Requests</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-user-plus fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- HR Management Tabs -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <ul class="nav nav-tabs card-header-tabs" id="hrTabs" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link active" id="employee-records-tab" data-bs-toggle="tab" data-bs-target="#employee-records" type="button" role="tab">
                                                        <i class="fas fa-id-card me-2"></i>Employee Records
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="payroll-tab" data-bs-toggle="tab" data-bs-target="#payroll" type="button" role="tab">
                                                        <i class="fas fa-money-bill-wave me-2"></i>Payroll
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="professional-dev-tab" data-bs-toggle="tab" data-bs-target="#professional-dev" type="button" role="tab">
                                                        <i class="fas fa-graduation-cap me-2"></i>Professional Development
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="evaluations-tab" data-bs-toggle="tab" data-bs-target="#evaluations" type="button" role="tab">
                                                        <i class="fas fa-star me-2"></i>Performance Evaluations
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="substitutes-tab" data-bs-toggle="tab" data-bs-target="#substitutes" type="button" role="tab">
                                                        <i class="fas fa-user-plus me-2"></i>Substitute Management
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="time-attendance-tab" data-bs-toggle="tab" data-bs-target="#time-attendance" type="button" role="tab">
                                                        <i class="fas fa-clock me-2"></i>Time & Attendance
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="card-body">
                                            <div class="tab-content" id="hrTabsContent">
                                                <!-- Employee Records Tab -->
                                                <div class="tab-pane fade show active" id="employee-records" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-plus-circle me-2"></i>Add New Employee</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showAddEmployeeModal()">
                                                                <i class="fas fa-user-plus me-2"></i>Add Employee
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportEmployeeRecords()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Employee ID</th>
                                                                    <th>Name</th>
                                                                    <th>Position</th>
                                                                    <th>Department</th>
                                                                    <th>Hire Date</th>
                                                                    <th>Status</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="employeeRecordsTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-users fa-3x mb-3"></i>
                                                                        <p>No employee records found. Add your first employee above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Payroll Tab -->
                                                <div class="tab-pane fade" id="payroll" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-money-bill-wave me-2"></i>Payroll Management</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="generatePayroll()">
                                                                <i class="fas fa-calculator me-2"></i>Generate Payroll
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportPayrollRecords()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-4">
                                                        <div class="col-md-4">
                                                            <div class="card bg-light">
                                                                <div class="card-body text-center">
                                                                    <h4 id="totalPayrollAmount">$0.00</h4>
                                                                    <p class="mb-0">Total Monthly Payroll</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="card bg-light">
                                                                <div class="card-body text-center">
                                                                    <h4 id="averageSalary">$0.00</h4>
                                                                    <p class="mb-0">Average Salary</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="card bg-light">
                                                                <div class="card-body text-center">
                                                                    <h4 id="payrollEmployeesCount">0</h4>
                                                                    <p class="mb-0">Employees on Payroll</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Employee</th>
                                                                    <th>Position</th>
                                                                    <th>Base Salary</th>
                                                                    <th>Benefits</th>
                                                                    <th>Deductions</th>
                                                                    <th>Net Pay</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="payrollTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-money-bill-wave fa-3x mb-3"></i>
                                                                        <p>No payroll records found. Generate payroll to get started.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Professional Development Tab -->
                                                <div class="tab-pane fade" id="professional-dev" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-graduation-cap me-2"></i>Professional Development</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showAddTrainingModal()">
                                                                <i class="fas fa-plus me-2"></i>Add Training
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportTrainingRecords()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Employee</th>
                                                                    <th>Training Program</th>
                                                                    <th>Type</th>
                                                                    <th>Start Date</th>
                                                                    <th>Completion Date</th>
                                                                    <th>Status</th>
                                                                    <th>Certification</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="professionalDevTable">
                                                                <tr>
                                                                    <td colspan="8" class="text-center text-muted">
                                                                        <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                                                                        <p>No training records found. Add training programs above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Performance Evaluations Tab -->
                                                <div class="tab-pane fade" id="evaluations" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-star me-2"></i>Performance Evaluations</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showCreateEvaluationModal()">
                                                                <i class="fas fa-plus me-2"></i>Create Evaluation
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportEvaluationRecords()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Employee</th>
                                                                    <th>Evaluation Period</th>
                                                                    <th>Overall Rating</th>
                                                                    <th>Evaluator</th>
                                                                    <th>Date</th>
                                                                    <th>Status</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="evaluationsTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-star fa-3x mb-3"></i>
                                                                        <p>No evaluations found. Create evaluations above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Substitute Management Tab -->
                                                <div class="tab-pane fade" id="substitutes" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-user-plus me-2"></i>Substitute Management</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showCreateSubstituteRequestModal()">
                                                                <i class="fas fa-plus me-2"></i>Request Substitute
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportSubstituteRecords()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Date</th>
                                                                    <th>Teacher</th>
                                                                    <th>Subject/Class</th>
                                                                    <th>Substitute</th>
                                                                    <th>Reason</th>
                                                                    <th>Status</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="substituteRequestsTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-user-plus fa-3x mb-3"></i>
                                                                        <p>No substitute requests found. Create requests above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Time & Attendance Tab -->
                                                <div class="tab-pane fade" id="time-attendance" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-clock me-2"></i>Time & Attendance</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showClockInModal()">
                                                                <i class="fas fa-sign-in-alt me-2"></i>Clock In/Out
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportAttendanceRecords()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-4">
                                                        <div class="col-md-3">
                                                            <div class="card bg-success text-white">
                                                                <div class="card-body text-center">
                                                                    <h4 id="presentTodayCount">0</h4>
                                                                    <p class="mb-0">Present Today</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="card bg-warning text-white">
                                                                <div class="card-body text-center">
                                                                    <h4 id="lateTodayCount">0</h4>
                                                                    <p class="mb-0">Late Today</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="card bg-danger text-white">
                                                                <div class="card-body text-center">
                                                                    <h4 id="absentTodayCount">0</h4>
                                                                    <p class="mb-0">Absent Today</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="card bg-info text-white">
                                                                <div class="card-body text-center">
                                                                    <h4 id="averageHoursWorked">0.0</h4>
                                                                    <p class="mb-0">Avg Hours/Day</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Employee</th>
                                                                    <th>Date</th>
                                                                    <th>Clock In</th>
                                                                    <th>Clock Out</th>
                                                                    <th>Hours Worked</th>
                                                                    <th>Status</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="timeAttendanceTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-clock fa-3x mb-3"></i>
                                                                        <p>No attendance records found. Clock in to get started.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Engagement Tab -->
                    <div class="tab-pane fade" id="studentEngagement">
                        <div class="container-fluid">
                            <h2 class="mb-4"><i class="fas fa-users me-2"></i>Student Engagement</h2>

                            <!-- Engagement Dashboard Overview -->
                            <div class="row mb-4">
                                <div class="col-md-2">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="totalClubsCount">0</h4>
                                                    <p class="mb-0">Active Clubs</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-users fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="totalSportsTeamsCount">0</h4>
                                                    <p class="mb-0">Sports Teams</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-futbol fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="upcomingEventsCount">0</h4>
                                                    <p class="mb-0">Upcoming Events</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-calendar fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="totalVolunteerHours">0</h4>
                                                    <p class="mb-0">Volunteer Hours</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-hands-helping fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-secondary text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="totalAwardsCount">0</h4>
                                                    <p class="mb-0">Awards Given</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-trophy fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="card bg-dark text-white">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h4 id="studentGovernmentCount">0</h4>
                                                    <p class="mb-0">Gov. Positions</p>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="fas fa-gavel fa-2x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Student Engagement Tabs -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <ul class="nav nav-tabs card-header-tabs" id="engagementTabs" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link active" id="clubs-tab" data-bs-toggle="tab" data-bs-target="#clubs" type="button" role="tab">
                                                        <i class="fas fa-users me-2"></i>Club Management
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="sports-tab" data-bs-toggle="tab" data-bs-target="#sports" type="button" role="tab">
                                                        <i class="fas fa-futbol me-2"></i>Sports Management
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="events-tab" data-bs-toggle="tab" data-bs-target="#events" type="button" role="tab">
                                                        <i class="fas fa-calendar me-2"></i>Event Planning
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="volunteer-tab" data-bs-toggle="tab" data-bs-target="#volunteer" type="button" role="tab">
                                                        <i class="fas fa-hands-helping me-2"></i>Volunteer Tracking
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="awards-tab" data-bs-toggle="tab" data-bs-target="#awards" type="button" role="tab">
                                                        <i class="fas fa-trophy me-2"></i>Awards & Recognition
                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="government-tab" data-bs-toggle="tab" data-bs-target="#government" type="button" role="tab">
                                                        <i class="fas fa-gavel me-2"></i>Student Government
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="card-body">
                                            <div class="tab-content" id="engagementTabsContent">
                                                <!-- Club Management Tab -->
                                                <div class="tab-pane fade show active" id="clubs" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-plus-circle me-2"></i>Club Management</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showAddClubModal()">
                                                                <i class="fas fa-plus me-2"></i>Add Club
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportClubData()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Club Name</th>
                                                                    <th>Advisor</th>
                                                                    <th>Members</th>
                                                                    <th>Meeting Day</th>
                                                                    <th>Status</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="clubsTable">
                                                                <tr>
                                                                    <td colspan="6" class="text-center text-muted">
                                                                        <i class="fas fa-users fa-3x mb-3"></i>
                                                                        <p>No clubs found. Add your first club above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Sports Management Tab -->
                                                <div class="tab-pane fade" id="sports" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-futbol me-2"></i>Sports Management</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showAddSportsTeamModal()">
                                                                <i class="fas fa-plus me-2"></i>Add Team
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportSportsData()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Team Name</th>
                                                                    <th>Sport</th>
                                                                    <th>Coach</th>
                                                                    <th>Players</th>
                                                                    <th>Season</th>
                                                                    <th>Status</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="sportsTeamsTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-futbol fa-3x mb-3"></i>
                                                                        <p>No sports teams found. Add your first team above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Event Planning Tab -->
                                                <div class="tab-pane fade" id="events" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-calendar me-2"></i>Event Planning</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showAddEventModal()">
                                                                <i class="fas fa-plus me-2"></i>Add Event
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportEventData()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Event Name</th>
                                                                    <th>Date</th>
                                                                    <th>Time</th>
                                                                    <th>Location</th>
                                                                    <th>Organizer</th>
                                                                    <th>Status</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="eventsTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-calendar fa-3x mb-3"></i>
                                                                        <p>No events found. Add your first event above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Volunteer Tracking Tab -->
                                                <div class="tab-pane fade" id="volunteer" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-hands-helping me-2"></i>Volunteer Tracking</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showAddVolunteerHoursModal()">
                                                                <i class="fas fa-plus me-2"></i>Log Hours
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportVolunteerData()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Student</th>
                                                                    <th>Activity</th>
                                                                    <th>Date</th>
                                                                    <th>Hours</th>
                                                                    <th>Organization</th>
                                                                    <th>Verified</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="volunteerTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-hands-helping fa-3x mb-3"></i>
                                                                        <p>No volunteer hours found. Log volunteer hours above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Awards & Recognition Tab -->
                                                <div class="tab-pane fade" id="awards" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-trophy me-2"></i>Awards & Recognition</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showAddAwardModal()">
                                                                <i class="fas fa-plus me-2"></i>Add Award
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportAwardData()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Student</th>
                                                                    <th>Award Type</th>
                                                                    <th>Award Name</th>
                                                                    <th>Date</th>
                                                                    <th>Category</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="awardsTable">
                                                                <tr>
                                                                    <td colspan="6" class="text-center text-muted">
                                                                        <i class="fas fa-trophy fa-3x mb-3"></i>
                                                                        <p>No awards found. Add your first award above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Student Government Tab -->
                                                <div class="tab-pane fade" id="government" role="tabpanel">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <h5><i class="fas fa-gavel me-2"></i>Student Government</h5>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <button class="btn btn-primary" onclick="showAddGovernmentPositionModal()">
                                                                <i class="fas fa-plus me-2"></i>Add Position
                                                            </button>
                                                            <button class="btn btn-outline-success ms-2" onclick="exportGovernmentData()">
                                                                <i class="fas fa-download me-2"></i>Export
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Position</th>
                                                                    <th>Student</th>
                                                                    <th>Class</th>
                                                                    <th>Term Start</th>
                                                                    <th>Term End</th>
                                                                    <th>Status</th>
                                                                    <th>Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="governmentTable">
                                                                <tr>
                                                                    <td colspan="7" class="text-center text-muted">
                                                                        <i class="fas fa-gavel fa-3x mb-3"></i>
                                                                        <p>No government positions found. Add positions above.</p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Curriculum Tab -->
                    <div class="tab-pane fade" id="curriculum">
                        <h2>Curriculum and Lesson Planning</h2>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Curriculum Builder</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="curriculumForm">
                                            <div class="mb-3">
                                                <label for="curriculumClass" class="form-label">Class</label>
                                                <select class="form-select" id="curriculumClass">
                                                    <option value="">Select Class</option>
                                                    <!-- Classes will be dynamically added here -->
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="curriculumSubject" class="form-label">Subject</label>
                                                <select class="form-select" id="curriculumSubject">
                                                    <option value="">Select Subject</option>
                                                    <!-- Subjects will be dynamically added here -->
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="curriculumTopic" class="form-label">Topic</label>
                                                <input type="text" class="form-control" id="curriculumTopic">
                                            </div>
                                            <div class="mb-3">
                                                <label for="curriculumDescription" class="form-label">Description</label>
                                                <textarea class="form-control" id="curriculumDescription" rows="3"></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-primary">Add to Curriculum</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Curriculum Overview</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="viewCurriculumClass" class="form-label">Class</label>
                                            <select class="form-select" id="viewCurriculumClass">
                                                <option value="">Select Class</option>
                                                <!-- Classes will be dynamically added here -->
                                            </select>
                                        </div>
                                        <div id="curriculumOverview">
                                            <p class="text-center text-muted">Select a class to view curriculum</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Calendar Tab -->
                    <div class="tab-pane fade" id="calendar">
                        <h2>School Calendar</h2>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Add Event</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="eventForm">
                                            <div class="mb-3">
                                                <label for="eventTitle" class="form-label">Event Title</label>
                                                <input type="text" class="form-control" id="eventTitle">
                                            </div>
                                            <div class="mb-3">
                                                <label for="eventDate" class="form-label">Date</label>
                                                <input type="date" class="form-control" id="eventDate">
                                            </div>
                                            <div class="mb-3">
                                                <label for="eventType" class="form-label">Event Type</label>
                                                <select class="form-select" id="eventType">
                                                    <option value="general">General</option>
                                                    <option value="exam">Examination</option>
                                                    <option value="holiday">Holiday</option>
                                                    <option value="meeting">Meeting</option>
                                                    <option value="activity">Activity</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="eventDescription" class="form-label">Description</label>
                                                <textarea class="form-control" id="eventDescription" rows="3"></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-primary">Add Event</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>School Calendar</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="schoolCalendar">
                                            <!-- Calendar will be displayed here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Settings Tab -->
                    <div class="tab-pane fade" id="systemSettings">
                        <h2>System Settings</h2>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Backup System Data</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>Create a backup of all system data. The backup file will be saved to your computer.</p>
                                        <p>Current Date and Time: <span id="backupDateTime" class="fw-bold"></span></p>
                                        <button id="createBackupBtn" class="btn btn-primary">
                                            <i class="fas fa-download me-2"></i> Create Backup
                                        </button>
                                        <div id="backupStatus" class="alert alert-info mt-3" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Restore System Data</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>Restore system data from a backup file. <span class="text-danger fw-bold">This will replace all current data!</span></p>
                                        <div class="mb-3">
                                            <label for="restoreFile" class="form-label">Select Backup File</label>
                                            <input type="file" class="form-control" id="restoreFile" accept=".json">
                                        </div>
                                        <p>Backup File Date: <span id="restoreFileDate" class="fw-bold">No file selected</span></p>
                                        <button id="restoreBackupBtn" class="btn btn-warning" disabled>
                                            <i class="fas fa-upload me-2"></i> Restore Backup
                                        </button>
                                        <div id="restoreStatus" class="alert alert-info mt-3" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Backup History</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Date & Time</th>
                                                        <th>Filename</th>
                                                        <th>Size</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="backupHistoryTable">
                                                    <tr>
                                                        <td colspan="4" class="text-center">No backup history available</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper - with offline fallback -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
            onerror="document.write('<script src=\'./libs/bootstrap.bundle.min.js\'><\/script>')"></script>

    <!-- Chart.js - with offline fallback -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"
            onerror="document.write('<script src=\'./libs/chart.min.js\'><\/script>')"></script>

    <!-- Schedule Modal -->
    <div class="modal fade" id="scheduleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduleModalTitle">Add Schedule Entry</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="scheduleForm">
                        <input type="hidden" id="scheduleId" value="">
                        <div class="mb-3">
                            <label for="scheduleDay" class="form-label">Day</label>
                            <select class="form-select" id="scheduleDay" required>
                                <option value="">Select Day</option>
                                <option value="Monday">Monday</option>
                                <option value="Tuesday">Tuesday</option>
                                <option value="Wednesday">Wednesday</option>
                                <option value="Thursday">Thursday</option>
                                <option value="Friday">Friday</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="schedulePeriod" class="form-label">Period</label>
                            <select class="form-select" id="schedulePeriod" required>
                                <option value="">Select Period</option>
                                <option value="1">Period 1</option>
                                <option value="2">Period 2</option>
                                <option value="3">Period 3</option>
                                <option value="4">Period 4</option>
                                <option value="5">Period 5</option>
                                <option value="6">Period 6</option>
                                <option value="7">Period 7</option>
                                <option value="8">Period 8</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="scheduleStartTime" class="form-label">Start Time</label>
                                    <input type="time" class="form-control" id="scheduleStartTime" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="scheduleEndTime" class="form-label">End Time</label>
                                    <input type="time" class="form-control" id="scheduleEndTime" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="scheduleTeacher" class="form-label">Teacher</label>
                            <select class="form-select" id="scheduleTeacher" required>
                                <option value="">Select Teacher</option>
                                <!-- Teachers will be dynamically added here -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="scheduleClass" class="form-label">Class</label>
                            <select class="form-select" id="scheduleClass" required>
                                <option value="">Select Class</option>
                                <!-- Classes will be dynamically added here -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="scheduleSubject" class="form-label">Subject</label>
                            <select class="form-select" id="scheduleSubject" required>
                                <option value="">Select Subject</option>
                                <!-- Subjects will be dynamically added here -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="scheduleRoom" class="form-label">Room</label>
                            <input type="text" class="form-control" id="scheduleRoom" placeholder="e.g., Room 101">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveScheduleBtn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Print Schedule Modal -->
    <div class="modal fade" id="printScheduleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Print Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="printScheduleTeacher" class="form-label">Select Teacher</label>
                        <select class="form-select" id="printScheduleTeacher" required>
                            <option value="">Select Teacher</option>
                            <!-- Teachers will be dynamically added here -->
                        </select>
                    </div>
                    <div id="schedulePreview" class="mt-3 p-3 border">
                        <p class="text-center text-muted">Select a teacher to preview their schedule</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="confirmPrintBtn">
                        <i class="fas fa-print me-1"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Performance Details Modal -->
    <div class="modal fade" id="performanceDetailsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="performanceModalTitle">Student Performance Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="performanceDetailsContent">
                        <!-- Performance details will be dynamically loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="exportPerformanceData()">
                        <i class="fas fa-download me-1"></i>Export List
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Storage Compatibility Layer -->
    <script src="./storage-compat.js"></script>

    <!-- Custom JS -->
    <script src="./auth.js"></script>
    <script src="./script.js"></script>
    <script src="./attendance.js"></script>
    <script src="./curriculum.js"></script>
    <script src="./calendar.js"></script>
    <script src="./backup.js"></script>
</body>
</html>
