/* OPTIMIZED PRINT STYLES FOR SCHOOL MANAGEMENT SYSTEM */
/* Updated 2024-12-19: Enhanced readability and proper scaling */

@media print {
    /* Enhanced Page Setup for Professional Printing */
    @page {
        size: A4 landscape;
        margin: 15mm; /* Professional margins */
        marks: none; /* Remove crop marks */
        bleed: 0mm; /* No bleed for school reports */
    }

    /* Enhanced Reset and Base Styles */
    * {
        box-sizing: border-box !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
        text-rendering: optimizeLegibility !important;
    }

    html, body {
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        width: 100% !important;
        height: 100% !important;
        font-family: 'Arial', 'Helvetica', sans-serif !important; /* Better print font */
        font-size: 12pt !important; /* Print-optimized size */
        line-height: 1.2 !important; /* Tighter line height for print */
        color: #000000 !important; /* Pure black for printing */
        font-weight: normal !important;
    }

    /* Hide all interface elements */
    body * {
        visibility: hidden;
    }

    /* Show only report cards and grade sheets */
    .report-card,
    .report-card *,
    #reportPreview,
    #reportPreview *,
    #gradeSheetPreview,
    #gradeSheetPreview * {
        visibility: visible !important;
        overflow: visible !important;
    }

    /* Enhanced Report Card Layout for Professional Printing */
    .report-card {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        font-family: 'Arial', 'Helvetica', sans-serif !important;
        font-size: 11pt !important;
        line-height: 1.1 !important;
        color: #000000 !important;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
        width: 28.1cm !important;
        height: 19.5cm !important;
        max-width: none !important;
        max-height: none !important;
        margin: 0 !important;
        padding: 6mm !important;
        box-shadow: none !important;
        border: 2px solid #000 !important;
        page-break-after: always !important;
        background: white !important;
        font-family: 'Times New Roman', serif !important;
        font-size: 12px !important;
        overflow: visible !important;
    }

    /* Headers */
    .report-card-header {
        text-align: center !important;
        border-bottom: 3px double #000 !important;
        padding-bottom: 8px !important;
        margin-bottom: 12px !important;
    }

    .school-name {
        font-size: 18px !important;
        font-weight: bold !important;
        margin: 0 0 4px 0 !important;
    }

    .school-address {
        font-size: 12px !important;
        margin: 2px 0 !important;
    }

    .report-title {
        font-size: 16px !important;
        font-weight: bold !important;
        text-decoration: underline !important;
        margin: 8px 0 0 0 !important;
    }

    /* Student Information */
    .student-info {
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 12px !important;
        font-size: 12px !important;
    }

    .student-photo {
        width: 80px !important;
        height: 80px !important;
        border: 2px solid #000 !important;
        object-fit: cover !important;
    }

    /* Grades Table - ENHANCED READABILITY */
    .grades-table {
        width: 100% !important;
        border-collapse: collapse !important;
        border: 2px solid #000 !important;
        font-size: 11px !important;
        margin-bottom: 12px !important;
    }

    .grades-table th,
    .grades-table td {
        border: 1px solid #000 !important;
        padding: 4px !important;
        text-align: center !important;
        font-size: 11px !important;
        line-height: 1.2 !important;
        vertical-align: middle !important;
    }

    .grades-table th {
        background-color: #e9ecef !important;
        font-weight: bold !important;
        font-size: 10px !important;
    }

    .grades-table .subject-cell {
        text-align: left !important;
        font-weight: bold !important;
        font-size: 10px !important;
        background-color: #f8f9fa !important;
    }

    .grades-table .semester-avg {
        background-color: #f0f8ff !important;
        font-weight: bold !important;
    }

    .grades-table .year-avg {
        background-color: #e6ffe6 !important;
        font-weight: bold !important;
        font-size: 12px !important;
    }

    .grades-table .overall-row {
        background-color: #fff3cd !important;
        font-weight: bold !important;
        border-top: 2px solid #000 !important;
    }

    /* COMPACT ATTENDANCE AND CONDUCT SECTIONS */
    .attendance-conduct {
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 8px !important;
        gap: 4px !important;
    }

    .attendance-section,
    .conduct-section {
        flex: 1 !important;
        border: 1px solid #000 !important;
        padding: 6px !important;
    }

    .section-title {
        font-size: 10px !important;
        margin: 0 0 4px 0 !important;
        font-weight: bold !important;
        text-decoration: underline !important;
        text-align: center !important;
    }

    .info-table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 9px !important;
    }

    .info-table td {
        padding: 2px 3px !important;
        line-height: 1.2 !important;
    }

    .info-table .label {
        font-weight: bold !important;
        width: 65% !important;
    }

    .info-table .value {
        border-bottom: 1px solid #000 !important;
        text-align: center !important;
        font-weight: bold !important;
    }

    /* Comments Section */
    .comments-section {
        margin-bottom: 12px !important;
        border: 1px solid #000 !important;
        padding: 8px !important;
    }

    .comments-content {
        min-height: 35px !important;
        padding: 6px !important;
        font-size: 10px !important;
        line-height: 1.3 !important;
        border: 1px solid #ccc !important;
        background-color: #fafafa !important;
    }

    /* Promotion Section */
    .promotion-section {
        margin-bottom: 12px !important;
        border: 1px solid #000 !important;
        padding: 8px !important;
        text-align: center !important;
    }

    /* Signatures Section */
    .signatures-section {
        display: flex !important;
        justify-content: space-between !important;
        margin-top: 12px !important;
        font-size: 10px !important;
    }

    .signature-box {
        text-align: center !important;
        width: 30% !important;
    }

    .signature-line {
        border-bottom: 2px solid #000 !important;
        margin-bottom: 6px !important;
        height: 35px !important;
        min-height: 35px !important;
    }

    .signature-title {
        font-weight: bold !important;
        font-size: 10px !important;
    }

    .signature-date {
        margin-top: 4px !important;
        font-size: 9px !important;
    }

    /* Footer */
    .report-footer {
        margin-top: 8px !important;
        font-size: 8px !important;
        text-align: center !important;
        border-top: 1px solid #ccc !important;
        padding-top: 4px !important;
        color: #666 !important;
    }

    /* Academic Summary */
    .academic-summary {
        margin-bottom: 8px !important;
        border: 1px solid #000 !important;
        padding: 6px !important;
        background-color: #f8f9fa !important;
    }

    /* Conduct Recommendations */
    .conduct-recommendations {
        margin-top: 4px !important;
    }

    .conduct-recommendations strong {
        font-size: 9px !important;
        font-weight: bold !important;
    }

    .conduct-recommendations ul {
        margin: 2px 0 !important;
        padding-left: 10px !important;
    }

    .conduct-recommendations li {
        font-size: 8px !important;
        line-height: 1.2 !important;
        margin-bottom: 1px !important;
    }

    /* Hide interface elements */
    .navbar,
    .nav-tabs,
    .card-header,
    .btn,
    .form-control,
    .form-select,
    .alert,
    .spinner-border,
    .position-fixed,
    .no-print,
    #reportActions,
    .card:not(.report-card) {
        display: none !important;
        visibility: hidden !important;
    }

    /* Ensure proper page breaks */
    .report-card:last-child {
        page-break-after: avoid !important;
    }

    /* Preserve failing grade indicators */
    .failing-grade,
    span[style*="color: red"] {
        color: #000 !important;
        font-weight: bold !important;
        text-decoration: underline !important;
    }

    /* CONSISTENT SIGNATURE POSITIONING */
    /* Grade Sheet Fixed Signature Sections */
    div[style*="position: absolute; bottom: 0"] {
        position: absolute !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        background-color: #f8f9fa !important;
        border-top: 1px solid #000 !important;
        padding: 6px !important;
        page-break-inside: avoid !important;
        height: 80px !important;
        min-height: 80px !important;
    }

    /* Grade Sheet Container with Fixed Height */
    div[style*="flex: 1; border: 2px solid #000; height: 100%"] {
        height: 100% !important;
        position: relative !important;
        overflow: visible !important;
        display: flex !important;
        flex-direction: column !important;
    }

    /* Grade Sheet Table Container with Bottom Padding */
    div[style*="flex: 1; overflow: visible; padding-bottom: 80px"] {
        flex: 1 !important;
        overflow: visible !important;
        padding-bottom: 80px !important;
    }

    /* Ensure signature lines are properly sized */
    div[style*="border-bottom: 2px solid #000; margin-bottom: 4px; height: 20px"] {
        border-bottom: 2px solid #000 !important;
        height: 20px !important;
        min-height: 20px !important;
        margin-bottom: 4px !important;
    }
}
