<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Database Subjects</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .debug-header {
            background: #007bff;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .subject-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .subject-item {
            background: #e9ecef;
            border: 1px solid #dee2e6;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
        }
        .database-subject {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .missing-subject {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .test-button {
            margin: 10px 5px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="text-center mb-4">🔍 Debug Database Subjects</h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-database me-2"></i>Database Subject Analysis</h5>
            <p class="mb-0">This page analyzes what subjects are actually stored in the database vs. what the Grade Entry Form is looking for. This will help us fix the subject matching issue.</p>
        </div>

        <!-- Test Buttons -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-play me-2"></i>Database Analysis Tools
            </div>
            
            <div class="text-center">
                <button id="loadDatabaseSubjects" class="btn btn-primary test-button">
                    <i class="fas fa-database me-2"></i>Load Database Subjects
                </button>
                <button id="testGradeOneSubjects" class="btn btn-warning test-button">
                    <i class="fas fa-child me-2"></i>Test Grade One Subjects
                </button>
                <button id="testGradeTwoSubjects" class="btn btn-warning test-button">
                    <i class="fas fa-child me-2"></i>Test Grade Two Subjects
                </button>
                <button id="testGradeSevenSubjects" class="btn btn-danger test-button">
                    <i class="fas fa-graduation-cap me-2"></i>Test Grade Seven Subjects
                </button>
            </div>
        </div>

        <!-- Database Subjects Display -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-list me-2"></i>Subjects Actually in Database
            </div>
            
            <div id="databaseSubjectsDisplay">
                <p class="text-muted">Click "Load Database Subjects" to see what's actually stored in the database.</p>
            </div>
        </div>

        <!-- Grade Entry Expectations -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-search me-2"></i>What Grade Entry Form is Looking For
            </div>
            
            <h6>Grade One Expected Subjects:</h6>
            <div class="subject-list">
                <div class="subject-item">BIBLE</div>
                <div class="subject-item">ENGLISH</div>
                <div class="subject-item">PHONICS</div>
                <div class="subject-item">READING</div>
                <div class="subject-item">SPELLING</div>
                <div class="subject-item">ARITHMETIC</div>
                <div class="subject-item">SCIENCE</div>
                <div class="subject-item">SOCIAL STUDIES</div>
                <div class="subject-item">WRITING</div>
                <div class="subject-item">PHYSICAL EDU.</div>
            </div>

            <h6>Grade Seven Expected Subjects:</h6>
            <div class="subject-list">
                <div class="subject-item">BIBLE</div>
                <div class="subject-item">ENGLISH</div>
                <div class="subject-item">LITERATURE</div>
                <div class="subject-item">MATHEMATICS</div>
                <div class="subject-item">SCIENCE</div>
                <div class="subject-item">SOCIAL STUDIES</div>
                <div class="subject-item">HEALTH SCIENCE</div>
                <div class="subject-item">PHYSICAL EDU.</div>
            </div>
        </div>

        <!-- Matching Results -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-check-double me-2"></i>Subject Matching Results
            </div>
            
            <div id="matchingResults">
                <p class="text-muted">Test different grades to see which subjects match between database and expectations.</p>
            </div>
        </div>

        <!-- Console Output -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-terminal me-2"></i>Console Output
            </div>
            
            <div class="console-output" id="consoleOutput">
Debug console output will appear here...
            </div>
        </div>

        <!-- Instructions -->
        <div class="alert alert-warning mt-4">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>How to Use This Debug Tool</h6>
            <ol>
                <li><strong>Load Database Subjects:</strong> Click to see what subjects are actually stored in the database</li>
                <li><strong>Test Grade Subjects:</strong> Click different grade buttons to see which subjects match</li>
                <li><strong>Check Console:</strong> Watch the console output for detailed matching information</li>
                <li><strong>Identify Issues:</strong> Green subjects = found in database, Red subjects = missing from database</li>
            </ol>
        </div>

        <div class="text-center mt-4">
            <a href="update on the school system.html" class="btn btn-success btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Grade Entry Form
            </a>
        </div>
    </div>

    <script>
        let databaseSubjects = [];
        
        // Mock database access (in real implementation, this would access IndexedDB)
        function logToConsole(message) {
            const consoleOutput = document.getElementById('consoleOutput');
            consoleOutput.innerHTML += message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(message);
        }

        // Mock function to simulate getting subjects from database
        function getMockDatabaseSubjects() {
            // These are the subjects that are typically in the database (from getBridgeOfHopeSubjects)
            return [
                { id: 1, name: 'BIBLE' },
                { id: 2, name: 'ENGLISH' },
                { id: 3, name: 'LITERATURE' },
                { id: 4, name: 'MATHEMATICS' },
                { id: 5, name: 'GEOGRAPHY' },
                { id: 6, name: 'HISTORY' },
                { id: 7, name: 'CIVICS' },
                { id: 8, name: 'GEN. SCIENCE' },
                { id: 9, name: 'CONFLICT MANG.' },
                { id: 10, name: 'HOME ECON.' },
                { id: 11, name: 'COMPUTER' },
                { id: 12, name: 'FRENCH' },
                { id: 13, name: 'PHYSICAL EDU.' }
            ];
        }

        // Mock function to get subjects for a specific class
        function getSubjectsForClass(className) {
            const subjectsByClass = {
                'Grade One': ['BIBLE', 'ENGLISH', 'PHONICS', 'READING', 'SPELLING', 'ARITHMETIC', 'SCIENCE', 'SOCIAL STUDIES', 'WRITING', 'PHYSICAL EDU.'],
                'Grade Two': ['BIBLE', 'ENGLISH', 'PHONICS', 'READING', 'SPELLING', 'ARITHMETIC', 'SCIENCE', 'SOCIAL STUDIES', 'WRITING', 'PHYSICAL EDU.'],
                'Grade Seven': ['BIBLE', 'ENGLISH', 'LITERATURE', 'MATHEMATICS', 'SCIENCE', 'SOCIAL STUDIES', 'HEALTH SCIENCE', 'PHYSICAL EDU.']
            };
            return subjectsByClass[className] || [];
        }

        function displayDatabaseSubjects() {
            databaseSubjects = getMockDatabaseSubjects();
            const display = document.getElementById('databaseSubjectsDisplay');
            
            let html = '<h6>Subjects Found in Database:</h6><div class="subject-list">';
            databaseSubjects.forEach(subject => {
                html += `<div class="subject-item database-subject">${subject.name}</div>`;
            });
            html += '</div>';
            html += `<p><strong>Total: ${databaseSubjects.length} subjects</strong></p>`;
            
            display.innerHTML = html;
            logToConsole(`✅ Loaded ${databaseSubjects.length} subjects from database`);
            databaseSubjects.forEach(subject => {
                logToConsole(`  - ${subject.name} (ID: ${subject.id})`);
            });
        }

        function testGradeSubjects(gradeName) {
            if (databaseSubjects.length === 0) {
                logToConsole('❌ Please load database subjects first');
                return;
            }

            const expectedSubjects = getSubjectsForClass(gradeName);
            logToConsole(`\n🧪 Testing ${gradeName} subjects...`);
            logToConsole(`Expected subjects: ${expectedSubjects.join(', ')}`);
            
            let foundCount = 0;
            let missingCount = 0;
            let foundSubjects = [];
            let missingSubjects = [];

            expectedSubjects.forEach(subjectName => {
                const found = databaseSubjects.find(s => s.name === subjectName);
                if (found) {
                    foundCount++;
                    foundSubjects.push(subjectName);
                    logToConsole(`  ✅ Found: ${subjectName}`);
                } else {
                    missingCount++;
                    missingSubjects.push(subjectName);
                    logToConsole(`  ❌ Missing: ${subjectName}`);
                }
            });

            // Display results
            const resultsDiv = document.getElementById('matchingResults');
            let html = `<h6>${gradeName} Subject Matching Results:</h6>`;
            html += `<p><strong>Found: ${foundCount}/${expectedSubjects.length} subjects</strong></p>`;
            
            if (foundSubjects.length > 0) {
                html += '<h6 class="text-success">✅ Found in Database:</h6><div class="subject-list">';
                foundSubjects.forEach(subject => {
                    html += `<div class="subject-item database-subject">${subject}</div>`;
                });
                html += '</div>';
            }
            
            if (missingSubjects.length > 0) {
                html += '<h6 class="text-danger">❌ Missing from Database:</h6><div class="subject-list">';
                missingSubjects.forEach(subject => {
                    html += `<div class="subject-item missing-subject">${subject}</div>`;
                });
                html += '</div>';
            }

            resultsDiv.innerHTML = html;
            
            logToConsole(`📊 Results: ${foundCount} found, ${missingCount} missing`);
            
            if (missingCount > 0) {
                logToConsole(`\n💡 Solution: Either add missing subjects to database or update curriculum to match database subjects`);
            } else {
                logToConsole(`\n🎉 All subjects found! This grade should work correctly.`);
            }
        }

        // Event listeners
        document.getElementById('loadDatabaseSubjects').addEventListener('click', displayDatabaseSubjects);
        document.getElementById('testGradeOneSubjects').addEventListener('click', () => testGradeSubjects('Grade One'));
        document.getElementById('testGradeTwoSubjects').addEventListener('click', () => testGradeSubjects('Grade Two'));
        document.getElementById('testGradeSevenSubjects').addEventListener('click', () => testGradeSubjects('Grade Seven'));

        // Initial message
        logToConsole('🔍 Database Subject Debug Tool Ready');
        logToConsole('📋 Click "Load Database Subjects" to start analysis');
    </script>
</body>
</html>
