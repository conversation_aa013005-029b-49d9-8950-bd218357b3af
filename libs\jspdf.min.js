// Minimal jsPDF placeholder for School Management System
// This is a placeholder to prevent fetch errors
// The actual PDF functionality will use browser's print-to-PDF feature

window.jsPDF = function() {
    console.log('jsPDF placeholder loaded - using browser print functionality instead');
    return {
        text: function() { return this; },
        save: function() { 
            console.log('PDF save requested - opening print dialog');
            window.print(); 
        },
        addPage: function() { return this; },
        setFontSize: function() { return this; },
        setFont: function() { return this; }
    };
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.jsPDF;
}
