<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enter Grades - Comprehensive Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-passed {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .test-failed {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .debug-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
        .workflow-step {
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .workflow-step.completed {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .workflow-step.failed {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">📝 Enter Grades - Comprehensive Test</h1>
        <p class="text-center text-muted">Complete workflow testing for the Grade Entry system</p>

        <!-- Test Status Dashboard -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary">Dynamic Loading</h5>
                        <h2 id="dynamicLoadingScore" class="text-primary">0/0</h2>
                        <small>Tests Passed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">Grade Entry</h5>
                        <h2 id="gradeEntryScore" class="text-success">0/0</h2>
                        <small>Tests Passed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning">Data Persistence</h5>
                        <h2 id="dataPersistenceScore" class="text-warning">0/0</h2>
                        <small>Tests Passed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">Validation</h5>
                        <h2 id="validationScore" class="text-info">0/0</h2>
                        <small>Tests Passed</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog me-2"></i>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <button id="runAllTests" class="btn btn-primary me-2">
                            <i class="fas fa-play me-1"></i>Run All Tests
                        </button>
                        <button id="runWorkflowTest" class="btn btn-success me-2">
                            <i class="fas fa-route me-1"></i>Test Full Workflow
                        </button>
                        <button id="clearData" class="btn btn-warning">
                            <i class="fas fa-trash me-1"></i>Clear Test Data
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database me-2"></i>Data Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <h4 id="studentCount" class="text-primary">0</h4>
                                <small>Students</small>
                            </div>
                            <div class="col-4">
                                <h4 id="gradeCount" class="text-success">0</h4>
                                <small>Grades</small>
                            </div>
                            <div class="col-4">
                                <h4 id="classCount" class="text-info">0</h4>
                                <small>Classes</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Workflow Test Section -->
        <div class="test-section">
            <h3><i class="fas fa-route text-primary me-2"></i>Complete Workflow Test</h3>
            <p>Test the complete grade entry workflow from start to finish</p>
            
            <div id="workflowSteps">
                <div class="workflow-step" id="step1">
                    <strong>Step 1:</strong> Initialize test data (students, classes)
                    <span class="float-end"><i class="fas fa-clock text-muted"></i></span>
                </div>
                <div class="workflow-step" id="step2">
                    <strong>Step 2:</strong> Select class and verify subject loading
                    <span class="float-end"><i class="fas fa-clock text-muted"></i></span>
                </div>
                <div class="workflow-step" id="step3">
                    <strong>Step 3:</strong> Verify student loading for selected class
                    <span class="float-end"><i class="fas fa-clock text-muted"></i></span>
                </div>
                <div class="workflow-step" id="step4">
                    <strong>Step 4:</strong> Select student, subject, and period
                    <span class="float-end"><i class="fas fa-clock text-muted"></i></span>
                </div>
                <div class="workflow-step" id="step5">
                    <strong>Step 5:</strong> Enter grade and submit
                    <span class="float-end"><i class="fas fa-clock text-muted"></i></span>
                </div>
                <div class="workflow-step" id="step6">
                    <strong>Step 6:</strong> Verify data persistence and retrieval
                    <span class="float-end"><i class="fas fa-clock text-muted"></i></span>
                </div>
            </div>
            
            <div class="mt-3">
                <div id="workflowResults" class="alert alert-info" style="display: none;">
                    <h6><i class="fas fa-info-circle me-2"></i>Workflow Results</h6>
                    <div id="workflowResultsContent"></div>
                </div>
            </div>
        </div>

        <!-- Interactive Test Form -->
        <div class="test-section">
            <h3><i class="fas fa-edit text-success me-2"></i>Interactive Grade Entry Test</h3>
            <p>Manually test the grade entry form functionality</p>
            
            <form id="testGradeForm">
                <div class="row">
                    <div class="col-md-3">
                        <label for="testClass" class="form-label">Class</label>
                        <select class="form-select" id="testClass">
                            <option value="">Select Class</option>
                            <option value="Grade One">Grade One</option>
                            <option value="Grade Two">Grade Two</option>
                            <option value="Grade Three">Grade Three</option>
                            <option value="Grade Four">Grade Four</option>
                            <option value="Grade Five">Grade Five</option>
                            <option value="Grade Six">Grade Six</option>
                            <option value="Grade Seven">Grade Seven</option>
                            <option value="Grade Eight">Grade Eight</option>
                            <option value="Grade Nine">Grade Nine</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="testStudent" class="form-label">Student</label>
                        <select class="form-select" id="testStudent">
                            <option value="">Select Student</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="testSubject" class="form-label">Subject</label>
                        <select class="form-select" id="testSubject">
                            <option value="">Select Subject</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="testPeriod" class="form-label">Period</label>
                        <select class="form-select" id="testPeriod">
                            <option value="">Select Period</option>
                            <option value="1ST P">1ST P</option>
                            <option value="2ND P">2ND P</option>
                            <option value="3RD P">3RD P</option>
                            <option value="EXAM 1">EXAM 1</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <label for="testGrade" class="form-label">Grade (0-100)</label>
                        <input type="number" class="form-control" id="testGrade" min="0" max="100">
                    </div>
                    <div class="col-md-6">
                        <label for="testComment" class="form-label">Comment (Optional)</label>
                        <input type="text" class="form-control" id="testComment">
                    </div>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Submit Test Grade
                    </button>
                    <button type="button" class="btn btn-info ms-2" onclick="showCurrentData()">
                        <i class="fas fa-eye me-1"></i>View Current Data
                    </button>
                </div>
            </form>
        </div>

        <!-- Debug Information -->
        <div class="test-section">
            <h3><i class="fas fa-terminal text-secondary me-2"></i>Debug Information</h3>
            <div id="debugInfo" class="debug-info">
                <p class="text-muted">Debug information will appear here...</p>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar text-info me-2"></i>Test Results</h3>
            <div id="testResults">
                <p class="text-muted">Run tests to see results...</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Mock schoolData for testing
        let schoolData = {
            students: [],
            grades: [],
            teachers: [],
            classes: []
        };

        // Load data from localStorage if available
        function loadTestData() {
            try {
                const savedData = localStorage.getItem('schoolData');
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    schoolData = { ...schoolData, ...parsedData };
                }
            } catch (error) {
                console.error('Error loading test data:', error);
            }
            updateDataStatus();
        }

        // Save data to localStorage
        function saveTestData() {
            try {
                localStorage.setItem('schoolData', JSON.stringify(schoolData));
            } catch (error) {
                console.error('Error saving test data:', error);
            }
        }

        // Initialize test data
        function initializeTestData() {
            const sampleStudents = [
                { id: 'student_1', name: 'Alice Johnson', class: 'Grade One', gender: 'Female', dateOfBirth: '2015-03-15' },
                { id: 'student_2', name: 'Bob Smith', class: 'Grade One', gender: 'Male', dateOfBirth: '2015-05-20' },
                { id: 'student_3', name: 'Carol Davis', class: 'Grade Two', gender: 'Female', dateOfBirth: '2014-08-10' },
                { id: 'student_4', name: 'David Wilson', class: 'Grade Two', gender: 'Male', dateOfBirth: '2014-11-25' },
                { id: 'student_5', name: 'Emma Brown', class: 'Grade Three', gender: 'Female', dateOfBirth: '2013-07-12' },
                { id: 'student_6', name: 'Frank Miller', class: 'Grade Four', gender: 'Male', dateOfBirth: '2012-09-18' },
                { id: 'student_7', name: 'Grace Taylor', class: 'Grade Five', gender: 'Female', dateOfBirth: '2011-12-03' },
                { id: 'student_8', name: 'Henry Anderson', class: 'Grade Six', gender: 'Male', dateOfBirth: '2010-04-22' }
            ];
            
            schoolData.students = sampleStudents;
            schoolData.grades = schoolData.grades || [];
            saveTestData();
            updateDataStatus();
            return true;
        }

        // Update data status display
        function updateDataStatus() {
            document.getElementById('studentCount').textContent = schoolData.students?.length || 0;
            document.getElementById('gradeCount').textContent = schoolData.grades?.length || 0;
            
            const uniqueClasses = schoolData.students ? 
                [...new Set(schoolData.students.map(s => s.class))].length : 0;
            document.getElementById('classCount').textContent = uniqueClasses;
        }

        // Get subjects for a class (mock function)
        function getSubjectsForClass(className) {
            const subjectsByClass = {
                'Grade One': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Writing', 'Physical Education'],
                'Grade Two': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Writing', 'Physical Education'],
                'Grade Three': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Writing', 'Physical Education'],
                'Grade Four': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Writing', 'Physical Education'],
                'Grade Five': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Health Science', 'Physical Education'],
                'Grade Six': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Health Science', 'Computer Science', 'French', 'Physical Education'],
                'Grade Seven': ['Bible', 'English', 'Literature', 'Mathematics', 'Geography', 'History', 'Civics', 'General Science', 'Conflict Management', 'Home Economics', 'Computer', 'French', 'Physical Education'],
                'Grade Eight': ['Bible', 'English', 'Literature', 'Mathematics', 'Geography', 'History', 'Civics', 'General Science', 'Conflict Management', 'Home Economics', 'Computer', 'French', 'Physical Education'],
                'Grade Nine': ['Bible', 'English', 'Literature', 'Mathematics', 'Geography', 'History', 'Civics', 'General Science', 'Conflict Management', 'Home Economics', 'Computer', 'French', 'Physical Education']
            };
            return subjectsByClass[className] || [];
        }

        // Continue with more test functions...
    </script>
</body>
</html>
