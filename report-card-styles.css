/* Professional Report Card Styles - A4 Landscape */

/* Perfect A4 Landscape Print Optimization */
@media print {
    @page {
        size: A4 landscape;
        margin: 6mm;
    }

    body {
        margin: 0;
        padding: 0;
        font-family: 'Times New Roman', serif;
        font-size: 8pt;
        line-height: 0.9;
        color: #000;
        background: white;
    }

    .report-card, .comprehensive-a4-layout {
        page-break-after: always;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 6mm !important;
        border: 2px solid #000 !important;
        box-shadow: none !important;
        box-sizing: border-box !important;
        font-size: 7pt !important;
        line-height: 0.9 !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: space-between !important;
        overflow: hidden !important;
    }

    .no-print {
        display: none !important;
    }

    /* Optimize all text sizes for print */
    h1, h2, h3, h4, h5, h6 {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Enhanced grades table print styles */
    table {
        border-collapse: collapse !important;
        width: 100% !important;
    }

    /* Grades table specific print optimization */
    .grades-table td, .grades-table th {
        border: 1.5px solid #000 !important;
        padding: 3px 2px !important;
        font-size: 10pt !important;
        font-weight: bold !important;
        vertical-align: middle !important;
    }

    /* Subject column */
    .grades-table td:first-child {
        font-size: 9pt !important;
        text-align: left !important;
        padding: 3px 4px !important;
    }

    /* Grade cells */
    .grades-table td:not(:first-child) {
        text-align: center !important;
        font-size: 10pt !important;
        min-height: 20px !important;
    }

    /* Average columns */
    .grades-table td[style*="background-color: #f0f8ff"] {
        background-color: #f0f8ff !important;
        font-size: 10pt !important;
    }

    /* Year average column */
    .grades-table td[style*="background-color: #e6ffe6"] {
        background-color: #e6ffe6 !important;
        font-size: 11pt !important;
    }

    /* Overall average row */
    .grades-table tr[style*="background-color: #fff3cd"] td {
        background-color: #fff3cd !important;
        font-size: 10pt !important;
        padding: 4px !important;
    }

    /* Overall average value */
    .grades-table tr[style*="background-color: #fff3cd"] td[style*="font-size: 14px"] {
        font-size: 12pt !important;
        font-weight: bold !important;
    }

    /* General table cells fallback */
    td, th {
        border: 1px solid #000 !important;
        padding: 2px !important;
        font-size: 9pt !important;
    }

    /* Compact layout print styles */
    .compact-comments-academic {
        display: flex !important;
        gap: 10px !important;
        margin-bottom: 10px !important;
        page-break-inside: avoid;
    }

    .compact-comments {
        flex: 2 !important;
        border: 1px solid #000 !important;
        padding: 6px !important;
        background-color: #f8f9fa !important;
    }

    .compact-academic-standing {
        flex: 1 !important;
        border: 1px solid #000 !important;
        padding: 6px !important;
        background-color: #fff3cd !important;
    }

    .compact-section-title {
        margin: 0 0 4px 0 !important;
        font-size: 8px !important;
        font-weight: bold !important;
        text-align: center !important;
        border-bottom: 1px solid #000 !important;
        padding-bottom: 2px !important;
    }

    .compact-comments-text {
        font-size: 7px !important;
        line-height: 1.2 !important;
        text-align: justify !important;
    }

    .compact-academic-text {
        text-align: center !important;
        font-size: 8px !important;
    }

    /* Compact grading scale print styles - Vertical List */
    .compact-grading-scale-vertical {
        margin-bottom: 6px !important;
        border: 1px solid #000 !important;
        padding: 4px !important;
        background-color: #f8f9fa !important;
        font-size: 7px !important;
        page-break-inside: avoid !important;
    }

    .compact-grading-scale-vertical h4 {
        margin: 0 0 3px 0 !important;
        font-size: 8px !important;
        font-weight: bold !important;
        text-align: left !important;
    }

    .compact-grading-scale-vertical div {
        line-height: 1.2 !important;
        margin-bottom: 1px !important;
        font-size: 7px !important;
    }

    .compact-grading-scale-vertical strong {
        font-weight: bold !important;
    }

    /* Ultra-compact attendance and conduct print styles */
    .ultra-compact-attendance-conduct {
        display: flex !important;
        gap: 6px !important;
        margin-bottom: 4px !important;
        page-break-inside: avoid !important;
    }

    .ultra-compact-attendance {
        flex: 1 !important;
        border: 1px solid #000 !important;
        padding: 2px !important;
        background-color: #f0f8ff !important;
        font-size: 7px !important;
    }

    .ultra-compact-conduct {
        flex: 1 !important;
        border: 1px solid #000 !important;
        padding: 2px !important;
        background-color: #fff3e0 !important;
        font-size: 7px !important;
    }

    .ultra-compact-title {
        margin: 0 0 1px 0 !important;
        font-size: 7px !important;
        font-weight: bold !important;
        text-align: center !important;
        border-bottom: 1px solid #000 !important;
        padding-bottom: 1px !important;
    }

    .ultra-compact-content {
        line-height: 1.0 !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 1px !important;
    }

    .ultra-compact-content span {
        font-size: 6px !important;
        width: 48% !important;
    }

    /* Enhanced signatures print styles */
    .signatures-section {
        margin-top: 8px !important;
        margin-bottom: 4px !important;
    }

    .signature-line {
        border-bottom: 1.5px solid #000 !important;
        height: 25px !important;
        margin-bottom: 3px !important;
    }

    .signature-title {
        font-size: 9pt !important;
        font-weight: bold !important;
        margin-bottom: 1px !important;
    }

    .signature-name {
        font-size: 8pt !important;
        margin-bottom: 2px !important;
    }

    .signature-date {
        font-size: 7pt !important;
    }
}

/* Screen display styles */
.report-card {
    font-family: 'Times New Roman', serif;
    background: white;
    border: 2px solid #000;
    width: 297mm;
    min-height: 210mm;
    padding: 15mm;
    margin: 20px auto;
    box-sizing: border-box;
    position: relative;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Header styles */
.report-header {
    text-align: center;
    border-bottom: 3px double #000;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.school-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border: 1px solid #ccc;
}

.school-name {
    font-size: 24px;
    font-weight: bold;
    color: #000;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
}

.school-address {
    font-size: 12px;
    color: #333;
    margin: 5px 0;
}

.report-title {
    font-size: 18px;
    font-weight: bold;
    color: #000;
    text-decoration: underline;
    margin: 10px 0 0 0;
}

.student-photo-placeholder {
    width: 80px;
    height: 80px;
    border: 2px solid #000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    text-align: center;
}

/* Student information styles */
.student-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 14px;
}

.student-info table {
    width: 100%;
    border-collapse: collapse;
}

.student-info td {
    padding: 5px;
}

.student-info .label {
    font-weight: bold;
    width: 120px;
}

.student-info .value {
    border-bottom: 1px solid #000;
}

/* Grading scale styles */
.grading-scale {
    margin-bottom: 20px;
    border: 2px solid #000;
    padding: 10px;
    background-color: #f9f9f9;
}

.grading-scale h4 {
    margin: 0 0 10px 0;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
}

.grading-scale-items {
    display: flex;
    justify-content: space-around;
    font-size: 12px;
    font-weight: bold;
}

/* Compact grading scale styles - Vertical List Format */
.compact-grading-scale-vertical {
    margin-bottom: 10px;
    border: 1px solid #000;
    padding: 6px;
    background-color: #f8f9fa;
    font-size: 8px;
    page-break-inside: avoid;
}

.compact-grading-scale-vertical h4 {
    margin: 0 0 4px 0;
    font-size: 9px;
    font-weight: bold;
    text-align: left;
}

.compact-grading-scale-vertical div {
    line-height: 1.3;
    margin-bottom: 1px;
}

.compact-grading-scale-vertical strong {
    font-weight: bold;
}

/* Academic performance table styles */
.performance-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #000;
    font-size: 11px;
    margin-bottom: 20px;
}

.performance-table th,
.performance-table td {
    border: 1px solid #000;
    padding: 6px;
    text-align: center;
}

.performance-table th {
    background-color: #e9ecef;
    font-weight: bold;
}

.performance-table .subject-name {
    text-align: left;
    font-weight: bold;
}

.performance-table .semester-header {
    background-color: #e9ecef;
    font-weight: bold;
}

.performance-table .summary-row {
    background-color: #e9ecef;
    font-weight: bold;
    border: 2px solid #000;
}

.performance-table .overall-average {
    background-color: #d4edda;
    font-size: 14px;
}

.performance-table .failing-grade {
    color: #dc3545;
    font-weight: bold;
}

/* Attendance and conduct section styles */
.attendance-conduct {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.attendance-section,
.conduct-section {
    flex: 1;
    border: 2px solid #000;
    padding: 15px;
}

.attendance-section {
    margin-right: 20px;
}

.section-title {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    text-decoration: underline;
}

.attendance-table,
.conduct-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.attendance-table td,
.conduct-table td {
    padding: 5px;
}

.attendance-table .label,
.conduct-table .label {
    font-weight: bold;
}

.attendance-table .value,
.conduct-table .value {
    border-bottom: 1px solid #000;
    text-align: center;
}

/* Comments section styles */
.comments-section {
    margin-bottom: 20px;
    border: 2px solid #000;
    padding: 15px;
}

.comments-content {
    min-height: 60px;
    border: 1px solid #ccc;
    padding: 10px;
    background-color: #fafafa;
    font-size: 12px;
    line-height: 1.4;
}

/* Compact layout styles for comments and academic standing */
.compact-comments-academic {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.compact-comments {
    flex: 2;
    border: 1px solid #000;
    padding: 8px;
    background-color: #f8f9fa;
}

.compact-academic-standing {
    flex: 1;
    border: 1px solid #000;
    padding: 8px;
    background-color: #fff3cd;
}

.compact-section-title {
    margin: 0 0 6px 0;
    font-size: 10px;
    font-weight: bold;
    text-align: center;
    border-bottom: 1px solid #ccc;
    padding-bottom: 3px;
}

.compact-comments-text {
    font-size: 9px;
    line-height: 1.3;
    text-align: justify;
}

.compact-academic-text {
    text-align: center;
    font-size: 10px;
}

/* Compact attendance and conduct styles */
.compact-attendance-conduct {
    display: flex;
    gap: 15px;
    margin-bottom: 12px;
}

.compact-attendance {
    flex: 1;
    border: 1px solid #000;
    padding: 6px;
    background-color: #f0f8ff;
    font-size: 8px;
}

.compact-conduct {
    flex: 1;
    border: 1px solid #000;
    padding: 6px;
    background-color: #fff3e0;
    font-size: 8px;
}

.compact-attendance-title,
.compact-conduct-title {
    margin: 0 0 4px 0;
    font-size: 9px;
    font-weight: bold;
    text-align: left;
}

.compact-attendance-content,
.compact-conduct-content {
    line-height: 1.3;
}

.compact-attendance-content div,
.compact-conduct-content div {
    margin-bottom: 1px;
}

.compact-attendance-content strong,
.compact-conduct-content strong {
    font-weight: bold;
}

/* Promotion status styles */
.promotion-status {
    margin-bottom: 20px;
    text-align: center;
    border: 2px solid #000;
    padding: 15px;
    background-color: #f0f8ff;
}

.promotion-status h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: bold;
}

.promotion-status .status {
    font-size: 14px;
    font-weight: bold;
}

.promotion-status .promoted {
    color: #28a745;
}

.promotion-status .pending {
    color: #dc3545;
}

/* Signature section styles */
.signature-section {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    font-size: 12px;
}

.signature-box {
    text-align: center;
    width: 30%;
}

.signature-line {
    border-bottom: 2px solid #000;
    margin-bottom: 5px;
    height: 40px;
}

.signature-title {
    font-weight: bold;
}

.signature-name {
    margin-top: 5px;
}

.signature-date {
    margin-top: 10px;
}

/* Footer styles */
.report-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 10px;
    color: #666;
    border-top: 1px solid #ccc;
    padding-top: 10px;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-bold {
    font-weight: bold;
}

.text-red {
    color: #dc3545;
}

.text-green {
    color: #28a745;
}

.bg-light {
    background-color: #f8f9fa;
}

.border-double {
    border: 3px double #000;
}

.border-solid {
    border: 2px solid #000;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 1200px) {
    .report-card {
        width: 95%;
        min-height: auto;
        padding: 10mm;
        margin: 10px auto;
    }
    
    .school-name {
        font-size: 20px;
    }
    
    .performance-table {
        font-size: 10px;
    }
    
    .attendance-conduct {
        flex-direction: column;
    }
    
    .attendance-section {
        margin-right: 0;
        margin-bottom: 20px;
    }
}

@media screen and (max-width: 768px) {
    .report-card {
        width: 100%;
        padding: 5mm;
        margin: 5px auto;
    }
    
    .student-info {
        flex-direction: column;
    }
    
    .student-info > div {
        margin-bottom: 15px;
    }
    
    .grading-scale-items {
        flex-direction: column;
        gap: 5px;
    }
    
    .signature-section {
        flex-direction: column;
        gap: 20px;
    }
    
    .signature-box {
        width: 100%;
    }
}
