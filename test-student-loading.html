<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Loading Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .debug-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9em;
        }
        .student-item {
            padding: 8px;
            margin: 4px 0;
            background-color: #e9ecef;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">👥 Student Loading Test</h1>
        <p class="text-center text-muted">Testing dynamic student loading in Grade Entry Form</p>

        <!-- Test Controls -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog me-2"></i>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <button id="debugData" class="btn btn-info me-2">
                            <i class="fas fa-bug me-1"></i>Debug Data
                        </button>
                        <button id="addSampleStudents" class="btn btn-success me-2">
                            <i class="fas fa-user-plus me-1"></i>Add Sample Students
                        </button>
                        <button id="clearStudents" class="btn btn-warning">
                            <i class="fas fa-trash me-1"></i>Clear Students
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>System Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 id="totalStudents" class="text-primary">0</h4>
                                <small>Total Students</small>
                            </div>
                            <div class="col-6">
                                <h4 id="uniqueClasses" class="text-success">0</h4>
                                <small>Classes</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grade Selection Test -->
        <div class="test-section">
            <h3><i class="fas fa-graduation-cap text-primary me-2"></i>Grade Selection Test</h3>
            <p>Test the dynamic student loading for each grade level</p>
            
            <div class="row">
                <div class="col-md-4">
                    <label for="testGradeSelect" class="form-label">Select Grade:</label>
                    <select class="form-select" id="testGradeSelect">
                        <option value="">Select Grade</option>
                        <option value="Grade One">Grade 1</option>
                        <option value="Grade Two">Grade 2</option>
                        <option value="Grade Three">Grade 3</option>
                        <option value="Grade Four">Grade 4</option>
                        <option value="Grade Five">Grade 5</option>
                        <option value="Grade Six">Grade 6</option>
                        <option value="Grade Seven">Grade 7</option>
                        <option value="Grade Eight">Grade 8</option>
                        <option value="Grade Nine">Grade 9</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="testStudentSelect" class="form-label">Available Students:</label>
                    <select class="form-select" id="testStudentSelect">
                        <option value="">Select a grade first</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Student Count:</label>
                    <div class="form-control-plaintext">
                        <span id="studentCount" class="badge bg-info">0</span> students available
                    </div>
                </div>
            </div>
            
            <div id="studentsList" class="mt-3" style="display: none;">
                <h6><i class="fas fa-users me-2"></i>Students in Selected Class</h6>
                <div id="studentsContainer"></div>
            </div>
        </div>

        <!-- Debug Information -->
        <div class="test-section">
            <h3><i class="fas fa-terminal text-secondary me-2"></i>Debug Information</h3>
            <div id="debugInfo" class="debug-info">
                <p class="text-muted">Click "Debug Data" to see system information...</p>
            </div>
        </div>

        <!-- Class Distribution -->
        <div class="test-section">
            <h3><i class="fas fa-chart-pie text-warning me-2"></i>Class Distribution</h3>
            <div id="classDistribution">
                <p class="text-muted">No data available</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Mock schoolData for testing
        let schoolData = {
            students: [],
            grades: [],
            teachers: [],
            classes: []
        };

        // Load data from localStorage if available
        function loadTestData() {
            try {
                const savedData = localStorage.getItem('schoolData');
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    schoolData = { ...schoolData, ...parsedData };
                }
            } catch (error) {
                console.error('Error loading test data:', error);
            }
        }

        // Save data to localStorage
        function saveTestData() {
            try {
                localStorage.setItem('schoolData', JSON.stringify(schoolData));
            } catch (error) {
                console.error('Error saving test data:', error);
            }
        }

        // Add sample students
        function addSampleStudents() {
            const sampleStudents = [
                { id: 'student_1', name: 'Alice Johnson', class: 'Grade One', gender: 'Female', dateOfBirth: '2015-03-15' },
                { id: 'student_2', name: 'Bob Smith', class: 'Grade One', gender: 'Male', dateOfBirth: '2015-05-20' },
                { id: 'student_3', name: 'Carol Davis', class: 'Grade Two', gender: 'Female', dateOfBirth: '2014-08-10' },
                { id: 'student_4', name: 'David Wilson', class: 'Grade Two', gender: 'Male', dateOfBirth: '2014-11-25' },
                { id: 'student_5', name: 'Emma Brown', class: 'Grade Three', gender: 'Female', dateOfBirth: '2013-07-12' },
                { id: 'student_6', name: 'Frank Miller', class: 'Grade Four', gender: 'Male', dateOfBirth: '2012-09-18' },
                { id: 'student_7', name: 'Grace Taylor', class: 'Grade Five', gender: 'Female', dateOfBirth: '2011-12-03' },
                { id: 'student_8', name: 'Henry Anderson', class: 'Grade Six', gender: 'Male', dateOfBirth: '2010-04-22' },
                { id: 'student_9', name: 'Ivy Thomas', class: 'Grade Seven', gender: 'Female', dateOfBirth: '2009-06-14' },
                { id: 'student_10', name: 'Jack White', class: 'Grade Eight', gender: 'Male', dateOfBirth: '2008-10-08' },
                { id: 'student_11', name: 'Kate Green', class: 'Grade One', gender: 'Female', dateOfBirth: '2015-01-30' },
                { id: 'student_12', name: 'Liam Blue', class: 'Grade Two', gender: 'Male', dateOfBirth: '2014-07-15' }
            ];
            
            schoolData.students = sampleStudents;
            saveTestData();
            updateSystemStatus();
            showNotification('Added 12 sample students', 'success');
        }

        // Clear all students
        function clearStudents() {
            schoolData.students = [];
            saveTestData();
            updateSystemStatus();
            document.getElementById('testStudentSelect').innerHTML = '<option value="">Select a grade first</option>';
            document.getElementById('studentsList').style.display = 'none';
            showNotification('Cleared all students', 'warning');
        }

        // Handle grade selection change
        function handleGradeChange(selectedGrade) {
            const studentSelect = document.getElementById('testStudentSelect');
            const studentCount = document.getElementById('studentCount');
            const studentsList = document.getElementById('studentsList');
            const studentsContainer = document.getElementById('studentsContainer');

            // Clear previous options
            studentSelect.innerHTML = '<option value="">Select Student</option>';

            if (selectedGrade && schoolData.students) {
                const studentsInClass = schoolData.students.filter(student => student.class === selectedGrade);
                
                // Populate students
                studentsInClass.forEach(student => {
                    const option = document.createElement('option');
                    option.value = student.id;
                    option.textContent = student.name;
                    studentSelect.appendChild(option);
                });

                // Update count
                studentCount.textContent = studentsInClass.length;
                studentCount.className = studentsInClass.length > 0 ? 'badge bg-success' : 'badge bg-danger';

                // Show students list
                if (studentsInClass.length > 0) {
                    studentsList.style.display = 'block';
                    const studentsHtml = studentsInClass.map(student => 
                        `<div class="student-item">
                            <strong>${student.name}</strong> (ID: ${student.id})
                            <br><small class="text-muted">Gender: ${student.gender}, DOB: ${student.dateOfBirth}</small>
                        </div>`
                    ).join('');
                    studentsContainer.innerHTML = studentsHtml;
                } else {
                    studentsList.style.display = 'none';
                }

                showNotification(`Found ${studentsInClass.length} students in ${selectedGrade}`, 
                    studentsInClass.length > 0 ? 'success' : 'warning');
            } else {
                studentsList.style.display = 'none';
                studentCount.textContent = '0';
                studentCount.className = 'badge bg-info';
            }
        }

        // Debug data function
        function debugData() {
            const debugInfo = document.getElementById('debugInfo');
            
            let debugText = `=== STUDENT DATA DEBUG ===\n`;
            debugText += `schoolData exists: ${!!schoolData}\n`;
            debugText += `schoolData.students exists: ${!!schoolData?.students}\n`;
            debugText += `schoolData.students is array: ${Array.isArray(schoolData?.students)}\n`;
            debugText += `Total students: ${schoolData?.students?.length || 0}\n\n`;
            
            if (schoolData?.students?.length > 0) {
                debugText += `Sample students:\n`;
                schoolData.students.slice(0, 5).forEach((student, index) => {
                    debugText += `${index + 1}. Name: "${student.name}", Class: "${student.class}", ID: "${student.id}"\n`;
                });
                
                const uniqueClasses = [...new Set(schoolData.students.map(s => s.class))];
                debugText += `\nUnique classes in system: ${uniqueClasses.join(', ')}\n`;
            } else {
                debugText += `No students found in system\n`;
            }
            debugText += `=== END DEBUG ===`;
            
            debugInfo.innerHTML = `<pre>${debugText}</pre>`;
        }

        // Update system status
        function updateSystemStatus() {
            const totalStudents = schoolData?.students?.length || 0;
            const uniqueClasses = schoolData?.students ? [...new Set(schoolData.students.map(s => s.class))].length : 0;
            
            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('uniqueClasses').textContent = uniqueClasses;
            
            // Update class distribution
            if (totalStudents > 0) {
                const classDistribution = {};
                schoolData.students.forEach(student => {
                    classDistribution[student.class] = (classDistribution[student.class] || 0) + 1;
                });
                
                const distributionHtml = Object.entries(classDistribution)
                    .map(([className, count]) => 
                        `<div class="d-flex justify-content-between align-items-center mb-2">
                            <span>${className}</span>
                            <span class="badge bg-primary">${count} students</span>
                        </div>`
                    ).join('');
                
                document.getElementById('classDistribution').innerHTML = distributionHtml;
            } else {
                document.getElementById('classDistribution').innerHTML = '<p class="text-muted">No students available</p>';
            }
        }

        // Simple notification function
        function showNotification(message, type) {
            // Create a simple alert for this test
            const alertClass = {
                'success': 'alert-success',
                'warning': 'alert-warning',
                'error': 'alert-danger',
                'info': 'alert-info'
            }[type] || 'alert-info';
            
            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', () => {
            loadTestData();
            updateSystemStatus();
            
            // Grade selection change
            document.getElementById('testGradeSelect').addEventListener('change', (e) => {
                handleGradeChange(e.target.value);
            });
            
            // Control buttons
            document.getElementById('debugData').addEventListener('click', debugData);
            document.getElementById('addSampleStudents').addEventListener('click', addSampleStudents);
            document.getElementById('clearStudents').addEventListener('click', clearStudents);
        });
    </script>
</body>
</html>
