<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Updated Report Card Layout</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .report-card {
            border: 2px solid #000;
            margin: 20px 0;
            background: white;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🧪 Updated Report Card Layout Test</h1>
        
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle me-2"></i>Changes Made</h5>
            <ul class="mb-0">
                <li>✅ Added PERFORMANCE section right under the grades table</li>
                <li>✅ Removed gaps between sections (changed margin-bottom from 5px to 3px)</li>
                <li>✅ Performance section shows: AVG, POS (position), and OF (total students in class)</li>
                <li>✅ All sections now flow directly under the grades table with minimal spacing</li>
            </ul>
        </div>

        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>New Layout Order</h5>
            <ol class="mb-0">
                <li><strong>Grades Table</strong> - All subjects with periods and averages</li>
                <li><strong>📊 PERFORMANCE</strong> - AVG: %, POS: x/y, OF: total students</li>
                <li><strong>📅 ATTENDANCE</strong> - Days, Present, Absent, Rate</li>
                <li><strong>🎯 CONDUCT</strong> - Score, Grade, Incidents, Status</li>
                <li><strong>💬 TEACHER'S COMMENTS</strong> - Teacher feedback</li>
                <li><strong>🎓 ACADEMIC STANDING</strong> - Promotion status</li>
                <li><strong>Signatures</strong> - Class Sponsor and Principal</li>
            </ol>
        </div>

        <div class="text-center">
            <h3>Test Instructions</h3>
            <p>To test the updated report card layout:</p>
            <ol class="text-start" style="max-width: 600px; margin: 0 auto;">
                <li>Go back to the main system: <a href="index.html" class="btn btn-primary btn-sm">Open Main System</a></li>
                <li>Navigate to the <strong>Reports</strong> tab</li>
                <li>Select a class and student</li>
                <li>Generate an individual report card</li>
                <li>Verify that the PERFORMANCE section appears right under the grades table</li>
                <li>Check that there are no large gaps between sections</li>
            </ol>
        </div>

        <div class="mt-4 p-3 bg-light border rounded">
            <h5>Expected PERFORMANCE Section Format:</h5>
            <div style="border: 2px solid #6f42c1; padding: 8px; background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%); border-radius: 3px; font-size: 12px; max-width: 400px;">
                <h6 style="margin: 0 0 4px 0; font-weight: bold; text-align: center; color: #4a148c; border-bottom: 1px solid #7b1fa2; padding-bottom: 2px;">📊 PERFORMANCE</h6>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 4px;">
                    <div style="background: rgba(255,255,255,0.7); padding: 2px 4px; border-radius: 2px; border: 1px solid #ce93d8; font-size: 10px;"><strong>AVG:</strong> 85%</div>
                    <div style="background: rgba(255,255,255,0.7); padding: 2px 4px; border-radius: 2px; border: 1px solid #ce93d8; font-size: 10px;"><strong>POS:</strong> 1/6</div>
                    <div style="background: rgba(255,255,255,0.7); padding: 2px 4px; border-radius: 2px; border: 1px solid #ce93d8; font-size: 10px;"><strong>OF:</strong> 6</div>
                </div>
            </div>
        </div>

        <div class="mt-4 text-center">
            <a href="index.html" class="btn btn-success btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Main System
            </a>
            <button class="btn btn-info btn-lg ms-2" onclick="window.print()">
                <i class="fas fa-print me-2"></i>Print This Test Page
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        console.log('✅ Updated Report Card Layout Test Page Loaded');
        console.log('📊 PERFORMANCE section has been added right under the grades table');
        console.log('🔧 Gaps between sections have been reduced from 5px to 3px');
        console.log('📋 Layout order: Grades → Performance → Attendance → Conduct → Comments → Academic Standing → Signatures');
    </script>
</body>
</html>
