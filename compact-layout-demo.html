<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compact Teacher Comments & Academic Standing Demo</title>
    <link rel="stylesheet" href="report-card-styles.css">
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            background: white;
            border: 2px solid #000;
            width: 297mm;
            min-height: 210mm;
            padding: 15mm;
            margin: 20px auto;
            box-sizing: border-box;
            position: relative;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .old-layout, .new-layout {
            flex: 1;
            border: 2px solid #ccc;
            padding: 15px;
            background: white;
        }
        
        .old-layout {
            border-color: #f44336;
        }
        
        .new-layout {
            border-color: #4caf50;
        }
        
        .layout-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 8px;
            border-radius: 4px;
        }
        
        .old-title {
            background: #ffebee;
            color: #c62828;
        }
        
        .new-title {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        /* Old layout styles */
        .old-comments-section {
            margin-bottom: 20px;
            border: 2px solid #000;
            padding: 15px;
        }
        
        .old-comments-content {
            min-height: 60px;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #fafafa;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .old-promotion-section {
            border: 2px solid #000;
            padding: 15px;
            background-color: #fff3cd;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>📝 Compact Report Card Layout Improvements</h1>
        <p><strong>Improved Design:</strong> Smaller, more efficient layout with better visual organization</p>
        <p><strong>Features:</strong> Compact Teacher Comments, Academic Standing, and Grading Scale</p>
        <p><strong>Benefits:</strong> Space-saving, professional appearance, better readability</p>
    </div>
    
    <div class="demo-container">
        <h2 style="text-align: center; margin-bottom: 30px;">Layout Comparison</h2>

        <!-- GRADING SCALE COMPARISON -->
        <h3 style="text-align: center; margin-bottom: 20px; color: #2c5aa0;">📊 Grading Scale Improvements</h3>

        <div class="comparison">
            <!-- OLD GRADING SCALE -->
            <div class="old-layout">
                <div class="layout-title old-title">❌ OLD GRADING SCALE (Large & Inefficient)</div>

                <div style="margin-bottom: 20px; border: 2px solid #000; padding: 10px; background-color: #f9f9f9;">
                    <h4 style="margin: 0 0 10px 0; text-align: center; font-size: 14px; font-weight: bold;">GRADING SCALE</h4>
                    <div style="font-size: 12px; font-weight: bold;">
                        <div style="margin-bottom: 5px;">A: 90-100% (Excellent)</div>
                        <div style="margin-bottom: 5px;">B: 80-89% (Very Good)</div>
                        <div style="margin-bottom: 5px;">C: 70-79% (Good)</div>
                        <div style="margin-bottom: 5px;">D: 60-69% (Satisfactory)</div>
                        <div style="margin-bottom: 5px;">F: Below 60% (Fail)</div>
                    </div>
                </div>
            </div>

            <!-- NEW GRADING SCALE -->
            <div class="new-layout">
                <div class="layout-title new-title">✅ NEW GRADING SCALE (Compact & Clean)</div>

                <!-- Compact Grading Scale Section (Vertical List) -->
                <div style="margin-bottom: 10px; border: 1px solid #000; padding: 6px; background-color: #f8f9fa; font-size: 8px;">
                    <h4 style="margin: 0 0 4px 0; font-size: 9px; font-weight: bold; text-align: left;">GRADING SCALE</h4>
                    <div style="line-height: 1.3;">
                        <div style="margin-bottom: 1px;"><strong>A:</strong> 90-100% (Excellent)</div>
                        <div style="margin-bottom: 1px;"><strong>B:</strong> 80-89% (Very Good)</div>
                        <div style="margin-bottom: 1px;"><strong>C:</strong> 70-79% (Good)</div>
                        <div style="margin-bottom: 1px;"><strong>D:</strong> 60-69% (Satisfactory)</div>
                        <div style="margin-bottom: 1px;"><strong>F:</strong> Below 60% (Fail)</div>
                    </div>
                </div>
            </div>
        </div>

        <h3 style="text-align: center; margin: 30px 0 20px 0; color: #2c5aa0;">📋 Attendance & Conduct Improvements</h3>

        <div class="comparison">
            <!-- OLD ATTENDANCE & CONDUCT -->
            <div class="old-layout">
                <div class="layout-title old-title">❌ OLD LAYOUT (Large Tables)</div>

                <!-- Old Attendance -->
                <div style="margin-bottom: 15px; border: 2px solid #000; padding: 10px;">
                    <h4 style="margin: 0 0 10px 0; font-size: 14px;">ATTENDANCE RECORD</h4>
                    <table style="width: 100%; font-size: 12px;">
                        <tr><td>Total School Days:</td><td>180</td></tr>
                        <tr><td>Days Present:</td><td>180</td></tr>
                        <tr><td>Days Absent:</td><td>0</td></tr>
                        <tr><td>Attendance Rate:</td><td>100%</td></tr>
                    </table>
                </div>

                <!-- Old Conduct -->
                <div style="border: 2px solid #000; padding: 10px;">
                    <h4 style="margin: 0 0 10px 0; font-size: 14px;">CONDUCT & BEHAVIOR</h4>
                    <table style="width: 100%; font-size: 12px;">
                        <tr><td>Conduct Score:</td><td>100/100</td></tr>
                        <tr><td>Conduct Grade:</td><td>A+ (Excellent)</td></tr>
                        <tr><td>Total Incidents:</td><td>0</td></tr>
                        <tr><td>Behavior Status:</td><td>Excellent</td></tr>
                    </table>
                    <div style="margin-top: 8px; font-size: 10px;">
                        <strong>Recommendations:</strong><br>
                        Excellent conduct record. Continue positive behavior.
                    </div>
                </div>
            </div>

            <!-- NEW ATTENDANCE & CONDUCT -->
            <div class="new-layout">
                <div class="layout-title new-title">✅ NEW LAYOUT (Compact Side-by-Side)</div>

                <!-- Compact Attendance and Conduct Section -->
                <div style="display: flex; gap: 15px; margin-bottom: 12px;">
                    <!-- Attendance Record -->
                    <div style="flex: 1; border: 1px solid #000; padding: 6px; background-color: #f0f8ff; font-size: 8px;">
                        <h4 style="margin: 0 0 4px 0; font-size: 9px; font-weight: bold; text-align: left;">ATTENDANCE RECORD</h4>
                        <div style="line-height: 1.3;">
                            <div style="margin-bottom: 1px;"><strong>Total School Days:</strong> 180</div>
                            <div style="margin-bottom: 1px;"><strong>Days Present:</strong> 180</div>
                            <div style="margin-bottom: 1px;"><strong>Days Absent:</strong> 0</div>
                            <div style="margin-bottom: 1px;"><strong>Attendance Rate:</strong> 100%</div>
                        </div>
                    </div>

                    <!-- Conduct & Behavior -->
                    <div style="flex: 1; border: 1px solid #000; padding: 6px; background-color: #fff3e0; font-size: 8px;">
                        <h4 style="margin: 0 0 4px 0; font-size: 9px; font-weight: bold; text-align: left;">CONDUCT & BEHAVIOR</h4>
                        <div style="line-height: 1.3;">
                            <div style="margin-bottom: 1px;"><strong>Conduct Score:</strong> 100/100</div>
                            <div style="margin-bottom: 1px;"><strong>Conduct Grade:</strong> A+ (Excellent)</div>
                            <div style="margin-bottom: 1px;"><strong>Total Incidents:</strong> 0</div>
                            <div style="margin-bottom: 1px;"><strong>Behavior Status:</strong> Excellent</div>
                            <div style="margin-top: 3px;">
                                <div style="font-size: 7px; font-weight: bold; margin-bottom: 1px;">Recommendations:</div>
                                <div style="font-size: 7px; line-height: 1.2;">
                                    Excellent conduct record. Continue positive behavior.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h3 style="text-align: center; margin: 30px 0 20px 0; color: #2c5aa0;">💬 Teacher Comments & Academic Standing</h3>
        
        <div class="comparison">
            <!-- OLD LAYOUT -->
            <div class="old-layout">
                <div class="layout-title old-title">❌ OLD LAYOUT (Large & Inefficient)</div>
                
                <div class="old-comments-section">
                    <h4 style="margin: 0 0 15px 0; font-size: 16px;">TEACHER'S COMMENTS & RECOMMENDATIONS</h4>
                    <div class="old-comments-content">
                        DAHN TYLER FRANCIS is making steady progress and shows improvement in academic performance.
                    </div>
                </div>
                
                <div class="old-promotion-section">
                    <h4 style="margin: 0 0 15px 0; font-size: 16px;">ACADEMIC STANDING</h4>
                    <div style="font-size: 14px; font-weight: bold; color: #dc3545;">
                        RETAINED IN GRADE ONE
                    </div>
                    <div style="margin-top: 5px; font-size: 11px; color: #666;">
                        Overall Average: 65%
                    </div>
                </div>
            </div>
            
            <!-- NEW LAYOUT -->
            <div class="new-layout">
                <div class="layout-title new-title">✅ NEW LAYOUT (Compact & Efficient)</div>
                
                <!-- Compact Comments and Academic Standing Section -->
                <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                    <!-- Teacher's Comments -->
                    <div style="flex: 2; border: 1px solid #000; padding: 8px; background-color: #f8f9fa;">
                        <h4 style="margin: 0 0 6px 0; font-size: 10px; font-weight: bold; text-align: center; border-bottom: 1px solid #ccc; padding-bottom: 3px;">TEACHER'S COMMENTS & RECOMMENDATIONS</h4>
                        <div style="font-size: 9px; line-height: 1.3; text-align: justify;">
                            DAHN TYLER FRANCIS is making steady progress and shows improvement in academic performance.
                        </div>
                    </div>
                    
                    <!-- Academic Standing -->
                    <div style="flex: 1; border: 1px solid #000; padding: 8px; background-color: #fff3cd;">
                        <h4 style="margin: 0 0 6px 0; font-size: 10px; font-weight: bold; text-align: center; border-bottom: 1px solid #ccc; padding-bottom: 3px;">ACADEMIC STANDING</h4>
                        <div style="text-align: center; margin-bottom: 8px;">
                            <div style="font-size: 10px; font-weight: bold; color: #dc3545; margin-bottom: 4px;">
                                RETAINED IN GRADE ONE
                            </div>
                            <div style="font-size: 8px; color: #666; margin-bottom: 4px;">
                                Overall Average: 65%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0; padding: 20px; background: white; border-radius: 10px; border: 1px solid #ddd;">
            <h3>✨ Improvements Achieved</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px; margin-top: 20px;">
                <div style="padding: 12px; border: 1px solid #4caf50; border-radius: 8px; background: #e8f5e8;">
                    <h4 style="color: #2e7d32; font-size: 14px;">📏 Space Efficiency</h4>
                    <p style="font-size: 12px; margin: 8px 0;">Reduced vertical space by ~70%</p>
                </div>
                <div style="padding: 12px; border: 1px solid #2196f3; border-radius: 8px; background: #e3f2fd;">
                    <h4 style="color: #1565c0; font-size: 14px;">👁️ Visual Design</h4>
                    <p style="font-size: 12px; margin: 8px 0;">Color-coded grades with better layout</p>
                </div>
                <div style="padding: 12px; border: 1px solid #ff9800; border-radius: 8px; background: #fff3e0;">
                    <h4 style="color: #ef6c00; font-size: 14px;">📖 Readability</h4>
                    <p style="font-size: 12px; margin: 8px 0;">Optimized fonts and spacing</p>
                </div>
                <div style="padding: 12px; border: 1px solid #9c27b0; border-radius: 8px; background: #f3e5f5;">
                    <h4 style="color: #7b1fa2; font-size: 14px;">🖨️ Print Quality</h4>
                    <p style="font-size: 12px; margin: 8px 0;">Perfect A4 landscape printing</p>
                </div>
                <div style="padding: 12px; border: 1px solid #f44336; border-radius: 8px; background: #ffebee;">
                    <h4 style="color: #c62828; font-size: 14px;">🎨 Color Coding</h4>
                    <p style="font-size: 12px; margin: 8px 0;">Grade letters with distinct colors</p>
                </div>
                <div style="padding: 12px; border: 1px solid #607d8b; border-radius: 8px; background: #eceff1;">
                    <h4 style="color: #37474f; font-size: 14px;">⚡ Performance</h4>
                    <p style="font-size: 12px; margin: 8px 0;">Faster rendering and loading</p>
                </div>
                <div style="padding: 12px; border: 1px solid #795548; border-radius: 8px; background: #efebe9;">
                    <h4 style="color: #5d4037; font-size: 14px;">📋 Attendance & Conduct</h4>
                    <p style="font-size: 12px; margin: 8px 0;">Side-by-side compact layout</p>
                </div>
                <div style="padding: 12px; border: 1px solid #3f51b5; border-radius: 8px; background: #e8eaf6;">
                    <h4 style="color: #303f9f; font-size: 14px;">📊 Data Density</h4>
                    <p style="font-size: 12px; margin: 8px 0;">More information in less space</p>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="window.print()">🖨️ Print Demo</button>
            <button class="btn btn-success" onclick="window.location.href='index.html'">🏠 Back to System</button>
        </div>
    </div>
    
    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Compact layout demo loaded successfully!');
            
            // Add hover effects
            const layouts = document.querySelectorAll('.old-layout, .new-layout');
            layouts.forEach(layout => {
                layout.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.2s ease';
                });
                
                layout.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
