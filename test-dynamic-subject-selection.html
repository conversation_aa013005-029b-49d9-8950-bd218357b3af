<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Subject Selection Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-passed {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .test-failed {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .curriculum-preview {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .subject-badge {
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">📚 Dynamic Subject Selection Test</h1>
        <p class="text-center text-muted">Testing grade-based subject filtering and curriculum mapping</p>

        <!-- Test Controls -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog me-2"></i>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <button id="runAllTests" class="btn btn-primary me-2">
                            <i class="fas fa-play me-1"></i>Run All Tests
                        </button>
                        <button id="clearResults" class="btn btn-outline-secondary">
                            <i class="fas fa-trash me-1"></i>Clear Results
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 id="passedCount" class="text-success">0</h4>
                                <small>Passed</small>
                            </div>
                            <div class="col-6">
                                <h4 id="failedCount" class="text-danger">0</h4>
                                <small>Failed</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grade Selection Test -->
        <div class="test-section">
            <h3><i class="fas fa-graduation-cap text-primary me-2"></i>Grade Selection Test</h3>
            <p>Test the dynamic subject loading for each grade level</p>
            
            <div class="row">
                <div class="col-md-4">
                    <label for="testGradeSelect" class="form-label">Select Grade:</label>
                    <select class="form-select" id="testGradeSelect">
                        <option value="">Select Grade</option>
                        <option value="Grade One">Grade 1</option>
                        <option value="Grade Two">Grade 2</option>
                        <option value="Grade Three">Grade 3</option>
                        <option value="Grade Four">Grade 4</option>
                        <option value="Grade Five">Grade 5</option>
                        <option value="Grade Six">Grade 6</option>
                        <option value="Grade Seven">Grade 7</option>
                        <option value="Grade Eight">Grade 8</option>
                        <option value="Grade Nine">Grade 9</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="testSubjectSelect" class="form-label">Available Subjects:</label>
                    <select class="form-select" id="testSubjectSelect">
                        <option value="">Select a grade first</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Subject Count:</label>
                    <div class="form-control-plaintext">
                        <span id="subjectCount" class="badge bg-info">0</span> subjects available
                    </div>
                </div>
            </div>
            
            <div id="curriculumPreview" class="curriculum-preview mt-3" style="display: none;">
                <h6><i class="fas fa-book me-2"></i>Curriculum Preview</h6>
                <div id="subjectsList"></div>
                <div id="specialSubjects" class="mt-2"></div>
            </div>
        </div>

        <!-- Curriculum Mapping Test -->
        <div class="test-section">
            <h3><i class="fas fa-list-check text-success me-2"></i>Curriculum Mapping Verification</h3>
            <div id="curriculumMappingResults"></div>
        </div>

        <!-- Subject Validation Test -->
        <div class="test-section">
            <h3><i class="fas fa-shield-alt text-warning me-2"></i>Subject Validation Test</h3>
            <div id="validationResults"></div>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h3><i class="fas fa-terminal text-secondary me-2"></i>Test Execution Log</h3>
            <div id="testLog" style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 4px;">
                <p class="text-muted">Click "Run All Tests" to begin testing...</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Mock the functions from the main script for testing
        function getSubjectsForClass(className) {
            const subjectsByClass = {
                'Grade One': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Writing', 'Physical Education'],
                'Grade Two': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Writing', 'Physical Education'],
                'Grade Three': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Writing', 'Physical Education'],
                'Grade Four': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Writing', 'Physical Education'],
                'Grade Five': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Health Science', 'Physical Education'],
                'Grade Six': ['Bible', 'English', 'Phonics', 'Reading', 'Spelling', 'Arithmetic', 'Science', 'Social Studies', 'Health Science', 'Computer Science', 'French', 'Physical Education'],
                'Grade Seven': ['Bible', 'English', 'Literature', 'Mathematics', 'Geography', 'History', 'Civics', 'General Science', 'Conflict Management', 'Home Economics', 'Computer', 'French', 'Physical Education'],
                'Grade Eight': ['Bible', 'English', 'Literature', 'Mathematics', 'Geography', 'History', 'Civics', 'General Science', 'Conflict Management', 'Home Economics', 'Computer', 'French', 'Physical Education'],
                'Grade Nine': ['Bible', 'English', 'Literature', 'Mathematics', 'Geography', 'History', 'Civics', 'General Science', 'Conflict Management', 'Home Economics', 'Computer', 'French', 'Physical Education']
            };
            return subjectsByClass[className] || [];
        }

        function isValidSubjectForClass(subject, className) {
            const validSubjects = getSubjectsForClass(className);
            return validSubjects.includes(subject);
        }

        // Test framework
        class DynamicSubjectTester {
            constructor() {
                this.testResults = { passed: 0, failed: 0 };
                this.testLog = [];
                this.setupEventListeners();
            }

            setupEventListeners() {
                // Grade selection change
                document.getElementById('testGradeSelect').addEventListener('change', (e) => {
                    this.handleGradeChange(e.target.value);
                });

                // Test controls
                document.getElementById('runAllTests').addEventListener('click', () => {
                    this.runAllTests();
                });

                document.getElementById('clearResults').addEventListener('click', () => {
                    this.clearResults();
                });
            }

            handleGradeChange(selectedGrade) {
                const subjectSelect = document.getElementById('testSubjectSelect');
                const subjectCount = document.getElementById('subjectCount');
                const curriculumPreview = document.getElementById('curriculumPreview');
                const subjectsList = document.getElementById('subjectsList');
                const specialSubjects = document.getElementById('specialSubjects');

                // Clear previous options
                subjectSelect.innerHTML = '<option value="">Select Subject</option>';

                if (selectedGrade) {
                    const subjects = getSubjectsForClass(selectedGrade);
                    
                    // Populate subjects
                    subjects.forEach(subject => {
                        const option = document.createElement('option');
                        option.value = subject;
                        option.textContent = subject;
                        subjectSelect.appendChild(option);
                    });

                    // Update count
                    subjectCount.textContent = subjects.length;
                    subjectCount.className = subjects.length > 0 ? 'badge bg-success' : 'badge bg-danger';

                    // Show curriculum preview
                    curriculumPreview.style.display = 'block';
                    
                    // Display subjects
                    const subjectsHtml = subjects.map(subject => 
                        `<span class="badge bg-primary subject-badge">${subject}</span>`
                    ).join('');
                    subjectsList.innerHTML = `<div><strong>All Subjects:</strong><br>${subjectsHtml}</div>`;

                    // Show special subjects for higher grades
                    const gradeNum = parseInt(selectedGrade.replace('Grade ', ''));
                    let specialSubjectsList = [];
                    
                    if (gradeNum >= 5) specialSubjectsList.push('Health Science');
                    if (gradeNum >= 6) specialSubjectsList.push('Computer Science', 'French');
                    if (gradeNum >= 7) specialSubjectsList.push('Literature', 'Mathematics', 'Geography', 'History', 'Civics', 'General Science', 'Conflict Management', 'Home Economics');

                    if (specialSubjectsList.length > 0) {
                        const specialHtml = specialSubjectsList.map(subject => 
                            `<span class="badge bg-warning text-dark subject-badge">${subject}</span>`
                        ).join('');
                        specialSubjects.innerHTML = `<div><strong>Special Subjects:</strong><br>${specialHtml}</div>`;
                    } else {
                        specialSubjects.innerHTML = '<div><em>No special subjects for this grade level</em></div>';
                    }

                    this.log(`Loaded ${subjects.length} subjects for ${selectedGrade}`, 'success');
                } else {
                    curriculumPreview.style.display = 'none';
                    subjectCount.textContent = '0';
                    subjectCount.className = 'badge bg-info';
                }
            }

            runAllTests() {
                this.log('Starting Dynamic Subject Selection Tests...', 'info');
                this.testResults = { passed: 0, failed: 0 };

                // Test curriculum mapping
                this.testCurriculumMapping();
                
                // Test subject validation
                this.testSubjectValidation();

                // Update results display
                this.updateResultsDisplay();
                this.log('All tests completed!', 'success');
            }

            testCurriculumMapping() {
                this.log('Testing curriculum mapping...', 'info');
                const results = [];

                const expectedCounts = {
                    'Grade One': 10, 'Grade Two': 10, 'Grade Three': 10, 'Grade Four': 10,
                    'Grade Five': 10, 'Grade Six': 12, 'Grade Seven': 13, 'Grade Eight': 13, 'Grade Nine': 13
                };

                Object.entries(expectedCounts).forEach(([grade, expectedCount]) => {
                    const subjects = getSubjectsForClass(grade);
                    const actualCount = subjects.length;
                    const passed = actualCount === expectedCount;
                    
                    if (passed) {
                        this.testResults.passed++;
                        results.push(`✅ ${grade}: ${actualCount} subjects (correct)`);
                    } else {
                        this.testResults.failed++;
                        results.push(`❌ ${grade}: ${actualCount} subjects (expected ${expectedCount})`);
                    }
                });

                document.getElementById('curriculumMappingResults').innerHTML = 
                    results.map(result => `<div class="mb-1">${result}</div>`).join('');
            }

            testSubjectValidation() {
                this.log('Testing subject validation...', 'info');
                const results = [];

                // Test valid subjects
                const validTests = [
                    ['Bible', 'Grade One', true],
                    ['French', 'Grade Six', true],
                    ['Literature', 'Grade Seven', true],
                    ['Health Science', 'Grade Five', true]
                ];

                // Test invalid subjects
                const invalidTests = [
                    ['French', 'Grade One', false],
                    ['Literature', 'Grade Four', false],
                    ['Computer Science', 'Grade Three', false],
                    ['Invalid Subject', 'Grade One', false]
                ];

                [...validTests, ...invalidTests].forEach(([subject, grade, shouldBeValid]) => {
                    const isValid = isValidSubjectForClass(subject, grade);
                    const passed = isValid === shouldBeValid;
                    
                    if (passed) {
                        this.testResults.passed++;
                        results.push(`✅ "${subject}" in ${grade}: ${isValid ? 'Valid' : 'Invalid'} (correct)`);
                    } else {
                        this.testResults.failed++;
                        results.push(`❌ "${subject}" in ${grade}: ${isValid ? 'Valid' : 'Invalid'} (expected ${shouldBeValid ? 'Valid' : 'Invalid'})`);
                    }
                });

                document.getElementById('validationResults').innerHTML = 
                    results.map(result => `<div class="mb-1">${result}</div>`).join('');
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                this.testLog.push({ timestamp, message, type });
                this.updateTestLog();
            }

            updateTestLog() {
                const logElement = document.getElementById('testLog');
                const logHtml = this.testLog.map(entry => {
                    const iconClass = {
                        'info': 'fas fa-info-circle text-info',
                        'success': 'fas fa-check-circle text-success',
                        'error': 'fas fa-times-circle text-danger'
                    }[entry.type] || 'fas fa-info-circle';
                    
                    return `<div class="mb-1">
                        <small class="text-muted">[${entry.timestamp}]</small>
                        <i class="${iconClass} me-1"></i>
                        ${entry.message}
                    </div>`;
                }).join('');
                logElement.innerHTML = logHtml;
                logElement.scrollTop = logElement.scrollHeight;
            }

            updateResultsDisplay() {
                document.getElementById('passedCount').textContent = this.testResults.passed;
                document.getElementById('failedCount').textContent = this.testResults.failed;
            }

            clearResults() {
                this.testLog = [];
                this.testResults = { passed: 0, failed: 0 };
                this.updateTestLog();
                this.updateResultsDisplay();
                
                // Clear result displays
                document.getElementById('curriculumMappingResults').innerHTML = '';
                document.getElementById('validationResults').innerHTML = '';
                
                // Reset form
                document.getElementById('testGradeSelect').value = '';
                document.getElementById('testSubjectSelect').innerHTML = '<option value="">Select a grade first</option>';
                document.getElementById('curriculumPreview').style.display = 'none';
                document.getElementById('subjectCount').textContent = '0';
                document.getElementById('subjectCount').className = 'badge bg-info';
            }
        }

        // Initialize tester when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new DynamicSubjectTester();
        });
    </script>
</body>
</html>
