<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Grade Entry Subjects</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .debug-header {
            background: #007bff;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .step-box {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            background: white;
        }
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .expected-subjects {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .subject-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 5px;
            margin: 10px 0;
        }
        .subject-item {
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
            text-align: center;
            font-size: 11px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="text-center mb-4">🐛 Debug Grade Entry Subjects Issue</h1>
        
        <div class="alert alert-warning">
            <h5><i class="fas fa-bug me-2"></i>Debugging Subject Dropdown Issue</h5>
            <p class="mb-0">This page helps debug why the subject dropdown is not populating when a class is selected in the Grade Entry Form. Open the browser console (F12) to see detailed debugging information.</p>
        </div>

        <!-- Debug Steps -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-list-ol me-2"></i>Debug Steps to Follow
            </div>
            
            <div class="step-box">
                <span class="step-number">1</span>
                <strong>Open Grade Entry Form:</strong>
                <p>Go to the main system and navigate to "Grade Entry" section</p>
                <a href="update on the school system.html" class="btn btn-primary btn-sm">Open Main System</a>
            </div>

            <div class="step-box">
                <span class="step-number">2</span>
                <strong>Open Browser Console:</strong>
                <p>Press F12 (Windows) or Cmd+Option+I (Mac) and click "Console" tab</p>
                <div class="console-output">
Press F12 → Console Tab
You should see debug messages like:
- "populateGradeEntryDropdowns called"
- "Grade class event listener attached successfully"
                </div>
            </div>

            <div class="step-box">
                <span class="step-number">3</span>
                <strong>Select a Class:</strong>
                <p>Select "Grade Two" from the class dropdown and watch console</p>
                <div class="console-output">
Expected console output:
- "Grade class changed: 2"
- "Found class: {id: 2, name: 'Grade Two', ...}"
- "Populating subjects for: Grade Two"
- "populateSubjectDropdownForClass called with: grade-subject Grade Two"
- "Class subjects for Grade Two: ['BIBLE', 'ENGLISH', 'PHONICS', ...]"
- "Available subjects in cache: ['BIBLE', 'ENGLISH', 'PHONICS', ...]"
- "Added subject: BIBLE"
- "Added subject: ENGLISH"
- ...
- "Total subjects added: 10"
                </div>
            </div>

            <div class="step-box">
                <span class="step-number">4</span>
                <strong>Check Subject Dropdown:</strong>
                <p>The subject dropdown should now show only Grade Two subjects</p>
                <div class="expected-subjects">
                    <h6>Expected Grade Two Subjects:</h6>
                    <div class="subject-list">
                        <div class="subject-item">BIBLE</div>
                        <div class="subject-item">ENGLISH</div>
                        <div class="subject-item">PHONICS</div>
                        <div class="subject-item">READING</div>
                        <div class="subject-item">SPELLING</div>
                        <div class="subject-item">ARITHMETIC</div>
                        <div class="subject-item">SCIENCE</div>
                        <div class="subject-item">SOCIAL STUDIES</div>
                        <div class="subject-item">WRITING</div>
                        <div class="subject-item">PHYSICAL EDUCATION</div>
                    </div>
                    <p><strong>Total: 10 subjects</strong></p>
                </div>
            </div>
        </div>

        <!-- Troubleshooting -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-wrench me-2"></i>Troubleshooting Guide
            </div>
            
            <h6>🚨 If No Console Messages Appear:</h6>
            <ul>
                <li><strong>Event Listener Not Attached:</strong> Look for "Grade class event listener attached successfully"</li>
                <li><strong>Element Not Found:</strong> Look for "Grade class dropdown not found!"</li>
                <li><strong>Cache Not Loaded:</strong> Refresh the page and try again</li>
            </ul>

            <h6>🚨 If Console Shows Errors:</h6>
            <ul>
                <li><strong>"Dropdown not found: grade-subject":</strong> Make sure you're in Grade Entry section</li>
                <li><strong>"Subject not found in cache: [subject name]":</strong> Subject names don't match database</li>
                <li><strong>"Class subjects for Grade Two: []":</strong> getSubjectsForClass function issue</li>
            </ul>

            <h6>🚨 If Subjects Still Don't Appear:</h6>
            <ul>
                <li><strong>Check Subject Names:</strong> Curriculum names must match database exactly</li>
                <li><strong>Verify Cache:</strong> Look for "Available subjects in cache" message</li>
                <li><strong>Force Refresh:</strong> Clear browser cache and reload</li>
            </ul>
        </div>

        <!-- Expected Behavior -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-check-circle me-2"></i>Expected Behavior
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>✅ What Should Happen:</h6>
                    <ol>
                        <li>Select "Grade Two" from class dropdown</li>
                        <li>Console shows debug messages</li>
                        <li>Subject dropdown populates with 10 subjects</li>
                        <li>Only Grade Two subjects are shown</li>
                        <li>No subjects from other grades appear</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6>🔍 Debug Information:</h6>
                    <ul>
                        <li>Event listener attachment confirmed</li>
                        <li>Class change detection working</li>
                        <li>Subject population function called</li>
                        <li>Curriculum function returning correct subjects</li>
                        <li>Subject matching and addition successful</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Test Different Grades -->
        <div class="debug-section">
            <div class="debug-header">
                <i class="fas fa-vial me-2"></i>Test Different Grades
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <h6>Grade One (10 subjects):</h6>
                    <div class="subject-list">
                        <div class="subject-item">BIBLE</div>
                        <div class="subject-item">ENGLISH</div>
                        <div class="subject-item">PHONICS</div>
                        <div class="subject-item">READING</div>
                        <div class="subject-item">SPELLING</div>
                        <div class="subject-item">ARITHMETIC</div>
                        <div class="subject-item">SCIENCE</div>
                        <div class="subject-item">SOCIAL STUDIES</div>
                        <div class="subject-item">WRITING</div>
                        <div class="subject-item">PHYSICAL EDUCATION</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>Grade Four (9 subjects):</h6>
                    <div class="subject-list">
                        <div class="subject-item">BIBLE</div>
                        <div class="subject-item">ENGLISH</div>
                        <div class="subject-item">PHONICS</div>
                        <div class="subject-item">READING</div>
                        <div class="subject-item">SPELLING</div>
                        <div class="subject-item">ARITHMETIC</div>
                        <div class="subject-item">SCIENCE</div>
                        <div class="subject-item">SOCIAL STUDIES</div>
                        <div class="subject-item">PHYSICAL EDUCATION</div>
                    </div>
                    <p><small>Note: No WRITING</small></p>
                </div>
                <div class="col-md-3">
                    <h6>Grade Six (12 subjects):</h6>
                    <div class="subject-list">
                        <div class="subject-item">BIBLE</div>
                        <div class="subject-item">ENGLISH</div>
                        <div class="subject-item">PHONICS</div>
                        <div class="subject-item">READING</div>
                        <div class="subject-item">SPELLING</div>
                        <div class="subject-item">ARITHMETIC</div>
                        <div class="subject-item">SCIENCE</div>
                        <div class="subject-item">SOCIAL STUDIES</div>
                        <div class="subject-item">HEALTH SCIENCE</div>
                        <div class="subject-item">COMPUTER SCIENCE</div>
                        <div class="subject-item">FRENCH</div>
                        <div class="subject-item">PHYSICAL EDUCATION</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>Grade Seven (13 subjects):</h6>
                    <div class="subject-list">
                        <div class="subject-item">BIBLE</div>
                        <div class="subject-item">ENGLISH</div>
                        <div class="subject-item">LITERATURE</div>
                        <div class="subject-item">MATHEMATICS</div>
                        <div class="subject-item">GEOGRAPHY</div>
                        <div class="subject-item">HISTORY</div>
                        <div class="subject-item">CIVICS</div>
                        <div class="subject-item">GENERAL SCIENCE</div>
                        <div class="subject-item">CONFLICT MANAGEMENT</div>
                        <div class="subject-item">HOME ECONOMICS</div>
                        <div class="subject-item">COMPUTER</div>
                        <div class="subject-item">FRENCH</div>
                        <div class="subject-item">PHYSICAL EDUCATION</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Fix -->
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-lightbulb me-2"></i>Quick Fix if Still Not Working</h6>
            <p>If the debugging shows that everything is working but subjects still don't appear, try:</p>
            <ol>
                <li>Refresh the page completely (Ctrl+F5 or Cmd+Shift+R)</li>
                <li>Clear browser cache and reload</li>
                <li>Check if subject names in database exactly match curriculum names</li>
                <li>Try selecting a different grade and then back to Grade Two</li>
            </ol>
        </div>

        <div class="text-center mt-4">
            <a href="update on the school system.html" class="btn btn-success btn-lg">
                <i class="fas fa-bug me-2"></i>Start Debugging in Main System
            </a>
        </div>
    </div>

    <script>
        console.log('🐛 Debug Grade Entry Subjects Page Loaded');
        console.log('📋 Follow the debug steps to identify the issue');
        console.log('🔍 Check console for detailed debugging information');
        console.log('✅ Expected: Subject dropdown should populate when class is selected');
    </script>
</body>
</html>
