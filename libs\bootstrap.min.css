/* Minimal Bootstrap CSS for School Management System */

/* Container and Grid System */
.container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
.container-fluid { width: 100%; padding: 0 15px; }
.row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
.col { flex: 1; padding: 0 15px; }
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Buttons */
.btn { display: inline-block; padding: 8px 16px; margin: 2px; border: 1px solid transparent; border-radius: 4px; text-decoration: none; cursor: pointer; font-size: 14px; }
.btn-primary { background-color: #007bff; color: white; border-color: #007bff; }
.btn-secondary { background-color: #6c757d; color: white; border-color: #6c757d; }
.btn-success { background-color: #28a745; color: white; border-color: #28a745; }
.btn-danger { background-color: #dc3545; color: white; border-color: #dc3545; }
.btn-warning { background-color: #ffc107; color: #212529; border-color: #ffc107; }
.btn-info { background-color: #17a2b8; color: white; border-color: #17a2b8; }
.btn-light { background-color: #f8f9fa; color: #212529; border-color: #f8f9fa; }
.btn-dark { background-color: #343a40; color: white; border-color: #343a40; }
.btn-sm { padding: 4px 8px; font-size: 12px; }
.btn-lg { padding: 12px 24px; font-size: 16px; }

/* Forms */
.form-control { display: block; width: 100%; padding: 8px 12px; font-size: 14px; border: 1px solid #ced4da; border-radius: 4px; }
.form-group { margin-bottom: 16px; }
.form-label { display: block; margin-bottom: 4px; font-weight: bold; }
.form-select { display: block; width: 100%; padding: 8px 12px; font-size: 14px; border: 1px solid #ced4da; border-radius: 4px; }

/* Cards */
.card { border: 1px solid #dee2e6; border-radius: 4px; margin-bottom: 16px; }
.card-header { padding: 12px 16px; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: bold; }
.card-body { padding: 16px; }
.card-footer { padding: 12px 16px; background-color: #f8f9fa; border-top: 1px solid #dee2e6; }

/* Tables */
.table { width: 100%; border-collapse: collapse; margin-bottom: 16px; }
.table th, .table td { padding: 8px; border-top: 1px solid #dee2e6; }
.table thead th { border-bottom: 2px solid #dee2e6; }
.table-striped tbody tr:nth-of-type(odd) { background-color: #f8f9fa; }
.table-bordered { border: 1px solid #dee2e6; }
.table-bordered th, .table-bordered td { border: 1px solid #dee2e6; }

/* Navigation */
.nav { display: flex; flex-wrap: wrap; list-style: none; margin: 0; padding: 0; }
.nav-link { display: block; padding: 8px 16px; text-decoration: none; color: #007bff; }
.nav-link:hover { color: #0056b3; }
.nav-tabs { border-bottom: 1px solid #dee2e6; }
.nav-tabs .nav-link { border: 1px solid transparent; border-top-left-radius: 4px; border-top-right-radius: 4px; }
.nav-tabs .nav-link.active { color: #495057; background-color: #fff; border-color: #dee2e6 #dee2e6 #fff; }

/* Utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.fw-bold { font-weight: bold !important; }
.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-light { background-color: #f8f9fa !important; }
.m-0 { margin: 0 !important; }
.m-1 { margin: 4px !important; }
.m-2 { margin: 8px !important; }
.m-3 { margin: 16px !important; }
.p-0 { padding: 0 !important; }
.p-1 { padding: 4px !important; }
.p-2 { padding: 8px !important; }
.p-3 { padding: 16px !important; }
.mb-1 { margin-bottom: 4px !important; }
.mb-2 { margin-bottom: 8px !important; }
.mb-3 { margin-bottom: 16px !important; }
.mt-1 { margin-top: 4px !important; }
.mt-2 { margin-top: 8px !important; }
.mt-3 { margin-top: 16px !important; }

/* Alerts */
.alert { padding: 12px 16px; margin-bottom: 16px; border: 1px solid transparent; border-radius: 4px; }
.alert-primary { color: #004085; background-color: #cce7ff; border-color: #b3d7ff; }
.alert-success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
.alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
.alert-warning { color: #856404; background-color: #fff3cd; border-color: #ffeaa7; }

/* Modal */
.modal { position: fixed; top: 0; left: 0; z-index: 1050; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
.modal-dialog { position: relative; width: auto; margin: 10px; max-width: 500px; }
.modal-content { position: relative; background-color: #fff; border: 1px solid #dee2e6; border-radius: 4px; }
.modal-header { display: flex; align-items: center; justify-content: space-between; padding: 16px; border-bottom: 1px solid #dee2e6; }
.modal-body { position: relative; flex: 1 1 auto; padding: 16px; }
.modal-footer { display: flex; align-items: center; justify-content: flex-end; padding: 16px; border-top: 1px solid #dee2e6; }

/* Progress */
.progress { display: flex; height: 16px; overflow: hidden; background-color: #e9ecef; border-radius: 4px; }
.progress-bar { display: flex; flex-direction: column; justify-content: center; color: #fff; text-align: center; background-color: #007bff; transition: width 0.6s ease; }

/* Responsive */
@media (max-width: 768px) {
    .col-1, .col-2, .col-3, .col-4, .col-6, .col-8, .col-12 { flex: 0 0 100%; max-width: 100%; }
    .container { padding: 0 10px; }
}
