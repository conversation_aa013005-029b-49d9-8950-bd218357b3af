<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conduct Report Debug Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .debug-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        
        .debug-section {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .debug-section.issue {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }
        
        .debug-section.solution {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .test-step {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        
        .console-output {
            background: #263238;
            color: #00e676;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 class="text-center mb-4">🐛 Conduct Report Debug Tool</h1>
        
        <!-- Issue Description -->
        <div class="debug-section issue">
            <h3><i class="fas fa-exclamation-triangle text-danger me-2"></i>Issue: Incident Summary Shows All Zeros</h3>
            
            <div class="alert alert-danger">
                <h6><i class="fas fa-bug me-2"></i>Problem Description</h6>
                <p>The Conduct Report's Incident Summary section always shows:</p>
                <div class="row text-center">
                    <div class="col-3"><strong>0</strong><br>Minor</div>
                    <div class="col-3"><strong>0</strong><br>Moderate</div>
                    <div class="col-3"><strong>0</strong><br>Major</div>
                    <div class="col-3"><strong>0</strong><br>Severe</div>
                </div>
                <p class="mt-3">Even when students have behavior incidents recorded in the system.</p>
            </div>
        </div>
        
        <!-- Root Cause Analysis -->
        <div class="debug-section">
            <h3><i class="fas fa-search me-2"></i>Root Cause Analysis</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>Possible Causes:</h6>
                    <ul>
                        <li><strong>Data Structure Mismatch:</strong> Behavior records not properly stored</li>
                        <li><strong>Student ID Mismatch:</strong> IDs not matching between records and students</li>
                        <li><strong>Date Filtering Issues:</strong> Records filtered out by date range</li>
                        <li><strong>Severity Field Issues:</strong> Severity values not matching expected strings</li>
                        <li><strong>Array Initialization:</strong> behaviorRecords array not properly initialized</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Debug Steps Added:</h6>
                    <ul>
                        <li>Enhanced logging in <code>generateConductReport()</code></li>
                        <li>Student ID matching verification</li>
                        <li>Date range filtering debug</li>
                        <li>Severity counting with detailed logs</li>
                        <li>New <code>debugConductReport()</code> function</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Solution Implementation -->
        <div class="debug-section solution">
            <h3><i class="fas fa-wrench text-success me-2"></i>Solution Implementation</h3>
            
            <div class="test-step">
                <h6><i class="fas fa-step-forward me-2"></i>Step 1: Enhanced Debugging</h6>
                <p>Added comprehensive logging to track the issue:</p>
                <div class="code-block">
// Enhanced generateConductReport() with debugging
console.log(`Generating conduct report for student ID: ${studentId}`);
console.log(`Total behavior records in system: ${schoolData.behaviorRecords.length}`);

// Check each record for student ID match
const allStudentBehaviorRecords = schoolData.behaviorRecords.filter(record => {
    const recordMatches = record.studentId === studentId;
    console.log(`Record ${record.id}: studentId match = ${recordMatches}`);
    return recordMatches;
});
                </div>
            </div>
            
            <div class="test-step">
                <h6><i class="fas fa-step-forward me-2"></i>Step 2: Debug Function</h6>
                <p>Added <code>debugConductReport(studentId)</code> function to help troubleshoot:</p>
                <div class="code-block">
// Usage in browser console:
debugConductReport('STU001'); // Replace with actual student ID

// This will show:
// - All behavior records in the system
// - Records filtered for the specific student
// - Detailed record information
// - Generated report data
                </div>
            </div>
            
            <div class="test-step">
                <h6><i class="fas fa-step-forward me-2"></i>Step 3: Data Validation</h6>
                <p>Added checks for data structure integrity:</p>
                <div class="code-block">
// Ensure behavior records exist
if (!schoolData.behaviorRecords) {
    schoolData.behaviorRecords = [];
}

// Validate student ID matching
const recordMatches = record.studentId === studentId;
console.log(`Record studentId = ${record.studentId}, target = ${studentId}`);
                </div>
            </div>
        </div>
        
        <!-- Testing Instructions -->
        <div class="debug-section">
            <h3><i class="fas fa-clipboard-check me-2"></i>Testing Instructions</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>Manual Testing Steps:</h6>
                    <ol>
                        <li><strong>Open the main application</strong> (index.html)</li>
                        <li><strong>Create test data:</strong>
                            <ul>
                                <li>Add a student (note the student ID)</li>
                                <li>Create 2-3 behavior incidents for that student</li>
                                <li>Use different severity levels (Minor, Moderate, Major)</li>
                            </ul>
                        </li>
                        <li><strong>Open browser console</strong> (F12)</li>
                        <li><strong>Run debug function:</strong>
                            <div class="code-block">debugConductReport('STUDENT_ID_HERE')</div>
                        </li>
                        <li><strong>Generate conduct report</strong> from Disciplinary Actions</li>
                        <li><strong>Check if incident counts are correct</strong></li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6>Console Output to Look For:</h6>
                    <div class="console-output">
=== DEBUGGING CONDUCT REPORT ===
Student ID: STU001
Total behavior records in system: 3
Record 1: {id: "BEH_123", studentId: "STU001", severity: "Minor"}
Record 2: {id: "BEH_124", studentId: "STU001", severity: "Major"}
Records for student STU001: 2
Incident counts - Minor: 1, Moderate: 0, Major: 1, Severe: 0
=== END DEBUG ===
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Common Issues & Solutions -->
        <div class="debug-section">
            <h3><i class="fas fa-lightbulb me-2"></i>Common Issues & Solutions</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Issue: No behavior records found</h6>
                        <p><strong>Cause:</strong> behaviorRecords array is empty or undefined</p>
                        <p><strong>Solution:</strong> Create some test behavior incidents first</p>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Issue: Student ID mismatch</h6>
                        <p><strong>Cause:</strong> Student IDs in records don't match student IDs in student list</p>
                        <p><strong>Solution:</strong> Check console logs for ID comparison</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Issue: Date filtering too restrictive</h6>
                        <p><strong>Cause:</strong> Records filtered out by date range</p>
                        <p><strong>Solution:</strong> Try generating report without date range</p>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Issue: Severity values don't match</h6>
                        <p><strong>Cause:</strong> Severity stored as different values than expected</p>
                        <p><strong>Solution:</strong> Check console logs for actual severity values</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Fix Commands -->
        <div class="debug-section">
            <h3><i class="fas fa-terminal me-2"></i>Quick Debug Commands</h3>
            
            <p>Copy and paste these commands in the browser console to debug:</p>
            
            <div class="code-block">
// 1. Check if behavior records exist
console.log('Behavior records:', schoolData.behaviorRecords);

// 2. Check total count
console.log('Total behavior records:', schoolData.behaviorRecords?.length || 0);

// 3. List all student IDs in behavior records
if (schoolData.behaviorRecords) {
    const studentIds = [...new Set(schoolData.behaviorRecords.map(r => r.studentId))];
    console.log('Student IDs with behavior records:', studentIds);
}

// 4. Check specific student (replace 'STU001' with actual ID)
debugConductReport('STU001');

// 5. Check severity values
if (schoolData.behaviorRecords) {
    const severities = [...new Set(schoolData.behaviorRecords.map(r => r.severity))];
    console.log('Severity values found:', severities);
}
            </div>
        </div>
        
        <!-- Expected Results -->
        <div class="debug-section">
            <h3><i class="fas fa-check-circle me-2"></i>Expected Results After Fix</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>Before Fix:</h6>
                    <div class="text-center p-3 bg-light border rounded">
                        <div class="row">
                            <div class="col-3"><h5 class="text-muted">0</h5><small>Minor</small></div>
                            <div class="col-3"><h5 class="text-muted">0</h5><small>Moderate</small></div>
                            <div class="col-3"><h5 class="text-muted">0</h5><small>Major</small></div>
                            <div class="col-3"><h5 class="text-muted">0</h5><small>Severe</small></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>After Fix:</h6>
                    <div class="text-center p-3 bg-light border rounded">
                        <div class="row">
                            <div class="col-3"><h5 class="text-warning">2</h5><small>Minor</small></div>
                            <div class="col-3"><h5 class="text-info">1</h5><small>Moderate</small></div>
                            <div class="col-3"><h5 class="text-danger">1</h5><small>Major</small></div>
                            <div class="col-3"><h5 class="text-dark">0</h5><small>Severe</small></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <a href="index.html" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-play me-2"></i>Test the Fix
            </a>
            <a href="integrated-conduct-reports-demo.html" class="btn btn-info btn-lg me-3">
                <i class="fas fa-chart-bar me-2"></i>View Integration Demo
            </a>
            <button class="btn btn-secondary btn-lg" onclick="window.print()">
                <i class="fas fa-print me-2"></i>Print Debug Guide
            </button>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🐛 Conduct Report Debug Tool loaded!');
            console.log('📊 Use the debug commands to troubleshoot conduct report issues.');
            
            // Add copy-to-clipboard functionality for code blocks
            const codeBlocks = document.querySelectorAll('.code-block');
            codeBlocks.forEach(block => {
                block.style.cursor = 'pointer';
                block.title = 'Click to copy to clipboard';
                block.addEventListener('click', function() {
                    navigator.clipboard.writeText(this.textContent).then(() => {
                        const originalBg = this.style.backgroundColor;
                        this.style.backgroundColor = '#d4edda';
                        setTimeout(() => {
                            this.style.backgroundColor = originalBg;
                        }, 500);
                    });
                });
            });
        });
    </script>
</body>
</html>
