/* Minimal FontAwesome CSS for School Management System */

/* Base icon styles */
.fa, .fas, .far, .fal, .fab {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Pro", "Font Awesome 5 Brands";
    font-weight: 900;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

/* Common icons used in the system */
.fa-user::before { content: "👤"; }
.fa-users::before { content: "👥"; }
.fa-graduation-cap::before { content: "🎓"; }
.fa-book::before { content: "📚"; }
.fa-calendar::before { content: "📅"; }
.fa-chart-bar::before { content: "📊"; }
.fa-cog::before { content: "⚙️"; }
.fa-settings::before { content: "⚙️"; }
.fa-home::before { content: "🏠"; }
.fa-dashboard::before { content: "📊"; }
.fa-edit::before { content: "✏️"; }
.fa-trash::before { content: "🗑️"; }
.fa-plus::before { content: "+"; }
.fa-minus::before { content: "-"; }
.fa-save::before { content: "💾"; }
.fa-print::before { content: "🖨️"; }
.fa-download::before { content: "⬇️"; }
.fa-upload::before { content: "⬆️"; }
.fa-search::before { content: "🔍"; }
.fa-filter::before { content: "🔽"; }
.fa-sort::before { content: "↕️"; }
.fa-check::before { content: "✓"; }
.fa-times::before { content: "✗"; }
.fa-eye::before { content: "👁️"; }
.fa-eye-slash::before { content: "🙈"; }
.fa-lock::before { content: "🔒"; }
.fa-unlock::before { content: "🔓"; }
.fa-envelope::before { content: "✉️"; }
.fa-phone::before { content: "📞"; }
.fa-address-book::before { content: "📇"; }
.fa-id-card::before { content: "🆔"; }
.fa-money-bill::before { content: "💵"; }
.fa-credit-card::before { content: "💳"; }
.fa-calculator::before { content: "🧮"; }
.fa-clipboard::before { content: "📋"; }
.fa-file::before { content: "📄"; }
.fa-file-pdf::before { content: "📄"; }
.fa-file-excel::before { content: "📊"; }
.fa-star::before { content: "⭐"; }
.fa-heart::before { content: "❤️"; }
.fa-thumbs-up::before { content: "👍"; }
.fa-thumbs-down::before { content: "👎"; }
.fa-warning::before { content: "⚠️"; }
.fa-exclamation::before { content: "❗"; }
.fa-info::before { content: "ℹ️"; }
.fa-question::before { content: "❓"; }
.fa-bell::before { content: "🔔"; }
.fa-clock::before { content: "🕐"; }
.fa-calendar-alt::before { content: "📅"; }
.fa-map-marker::before { content: "📍"; }
.fa-globe::before { content: "🌐"; }
.fa-wifi::before { content: "📶"; }
.fa-signal::before { content: "📶"; }
.fa-battery-full::before { content: "🔋"; }
.fa-power-off::before { content: "⏻"; }
.fa-refresh::before { content: "🔄"; }
.fa-sync::before { content: "🔄"; }
.fa-undo::before { content: "↶"; }
.fa-redo::before { content: "↷"; }
.fa-copy::before { content: "📋"; }
.fa-paste::before { content: "📄"; }
.fa-cut::before { content: "✂️"; }
.fa-link::before { content: "🔗"; }
.fa-unlink::before { content: "⛓️"; }
.fa-share::before { content: "📤"; }
.fa-export::before { content: "📤"; }
.fa-import::before { content: "📥"; }
.fa-database::before { content: "🗄️"; }
.fa-server::before { content: "🖥️"; }
.fa-cloud::before { content: "☁️"; }
.fa-folder::before { content: "📁"; }
.fa-folder-open::before { content: "📂"; }
.fa-archive::before { content: "🗃️"; }
.fa-tag::before { content: "🏷️"; }
.fa-tags::before { content: "🏷️"; }
.fa-bookmark::before { content: "🔖"; }
.fa-flag::before { content: "🚩"; }
.fa-pin::before { content: "📌"; }
.fa-paperclip::before { content: "📎"; }
.fa-image::before { content: "🖼️"; }
.fa-photo::before { content: "📷"; }
.fa-camera::before { content: "📷"; }
.fa-video::before { content: "📹"; }
.fa-music::before { content: "🎵"; }
.fa-volume-up::before { content: "🔊"; }
.fa-volume-down::before { content: "🔉"; }
.fa-volume-off::before { content: "🔇"; }
.fa-microphone::before { content: "🎤"; }
.fa-headphones::before { content: "🎧"; }
.fa-keyboard::before { content: "⌨️"; }
.fa-mouse::before { content: "🖱️"; }
.fa-desktop::before { content: "🖥️"; }
.fa-laptop::before { content: "💻"; }
.fa-tablet::before { content: "📱"; }
.fa-mobile::before { content: "📱"; }
.fa-gamepad::before { content: "🎮"; }
.fa-tv::before { content: "📺"; }
.fa-radio::before { content: "📻"; }

/* Size modifiers */
.fa-xs { font-size: 0.75em; }
.fa-sm { font-size: 0.875em; }
.fa-lg { font-size: 1.33333em; line-height: 0.75em; vertical-align: -0.0667em; }
.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }
.fa-4x { font-size: 4em; }
.fa-5x { font-size: 5em; }

/* Rotation and flipping */
.fa-rotate-90 { transform: rotate(90deg); }
.fa-rotate-180 { transform: rotate(180deg); }
.fa-rotate-270 { transform: rotate(270deg); }
.fa-flip-horizontal { transform: scaleX(-1); }
.fa-flip-vertical { transform: scaleY(-1); }

/* Animation */
.fa-spin { animation: fa-spin 2s infinite linear; }
.fa-pulse { animation: fa-pulse 1s infinite steps(8); }

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fa-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Fixed width */
.fa-fw { width: 1.25em; text-align: center; }

/* List icons */
.fa-ul { list-style-type: none; margin-left: 2.5em; padding-left: 0; }
.fa-ul > li { position: relative; }
.fa-li { position: absolute; left: -2em; width: 2em; top: 0.14286em; text-align: center; }

/* Bordered and pulled */
.fa-border { padding: 0.2em 0.25em 0.15em; border: solid 0.08em #eee; border-radius: 0.1em; }
.fa-pull-left { float: left; margin-right: 0.3em; }
.fa-pull-right { float: right; margin-left: 0.3em; }

/* Stacked icons */
.fa-stack { position: relative; display: inline-block; width: 2em; height: 2em; line-height: 2em; vertical-align: middle; }
.fa-stack-1x, .fa-stack-2x { position: absolute; left: 0; width: 100%; text-align: center; }
.fa-stack-1x { line-height: inherit; }
.fa-stack-2x { font-size: 2em; }

/* Inverse */
.fa-inverse { color: #fff; }
